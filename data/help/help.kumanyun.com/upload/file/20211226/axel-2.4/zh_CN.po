msgid ""
msgstr ""
"Project-Id-Version: Axel\n"
"Report-Msgid-Bugs-To: \n"
"POT-Creation-Date: 2009-04-27 13:21+0530\n"
"PO-Revision-Date: 2008-11-08 22:40+0700\n"
"Last-Translator: <PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: Simplified Chinese <<EMAIL>>\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=utf-8\n"
"Content-Transfer-Encoding: 8bit\n"
"Last-Revision: Li Jin <<EMAIL>>\n"

#: axel.c:55
msgid "Buffer resized for this speed."
msgstr "为这个速率调整缓冲区大小。"

#: axel.c:91
msgid "Could not parse URL.\n"
msgstr "无法解析URL\n"

#: axel.c:126
#, fuzzy, c-format
msgid "File size: %lld bytes"
msgstr "文件大小: %i 字节"

#: axel.c:143
#, c-format
msgid "Opening output file %s"
msgstr "打开输出文件 %s"

#: axel.c:152
msgid "Server unsupported, starting from scratch with one connection."
msgstr "服务器不支持，开始一个连接。"

#: axel.c:171
#, fuzzy, c-format
msgid "State file found: %lld bytes downloaded, %lld to go."
msgstr "找到状态文件： %i 字节已下载，继续下载 %i 字节。"

#: axel.c:178 axel.c:190
msgid "Error opening local file"
msgstr "打开本地文件错误"

#: axel.c:202
msgid "Crappy filesystem/OS.. Working around. :-("
msgstr ""
"糟糕的文件系统或操作系统……Working around……（译注德国写的句子，实在不知道如何"
"翻译……）:-("

#: axel.c:235
msgid "Starting download"
msgstr "开始下载"

#: axel.c:242 axel.c:401
#, c-format
msgid "Connection %i downloading from %s:%i using interface %s"
msgstr "连接 %i 从 %s:%i 通过接口 %s 下载"

#: axel.c:249 axel.c:411
msgid "pthread error!!!"
msgstr "线程错误！！！"

#: axel.c:317
#, c-format
msgid "Error on connection %i! Connection closed"
msgstr "连接 %i 有错误！ 连接中断"

#: axel.c:331
#, c-format
msgid "Connection %i unexpectedly closed"
msgstr "连接 %i 被异常中断"

#: axel.c:335 axel.c:352
#, c-format
msgid "Connection %i finished"
msgstr "连接 %i 结束"

#: axel.c:364
msgid "Write error!"
msgstr "写错误！"

#: axel.c:376
#, c-format
msgid "Connection %i timed out"
msgstr "连接超时 %i"

#: conf.c:107
#, c-format
msgid "Error in %s line %i.\n"
msgstr "%s %i 行有错误。\n"

#: conn.c:349 ftp.c:124
#, c-format
msgid "Too many redirects.\n"
msgstr "太多重定向。\n"

#: conn.c:368
#, c-format
msgid "Unknown HTTP error.\n"
msgstr "未知 HTTP 错误。\n"

#: ftp.c:35 http.c:60
#, c-format
msgid "Unable to connect to server %s:%i\n"
msgstr "不能连接到服务器 %s:%i\n"

#: ftp.c:91
#, c-format
msgid "Can't change directory to %s\n"
msgstr "不能变更目录到 %s\n"

#: ftp.c:117 ftp.c:177
#, c-format
msgid "File not found.\n"
msgstr "找不到文件。\n"

#: ftp.c:179
#, c-format
msgid "Multiple matches for this URL.\n"
msgstr "这个 URL 有多个匹配。\n"

#: ftp.c:250 ftp.c:256
#, c-format
msgid "Error opening passive data connection.\n"
msgstr "打开主动数据连接错误。\n"

#: ftp.c:286
#, c-format
msgid "Error writing command %s\n"
msgstr "写命令出错 %s\n"

#: ftp.c:311 http.c:150
#, c-format
msgid "Connection gone.\n"
msgstr "连接继续。\n"

#: http.c:45
#, c-format
msgid "Invalid proxy string: %s\n"
msgstr "代理字符串无效： %s\n"

#: text.c:154
#, c-format
msgid "Can't redirect stdout to /dev/null.\n"
msgstr "stdout 不能重定向到 /dev/null 。\n"

#: text.c:176
#, c-format
msgid "Error when trying to read URL (Too long?).\n"
msgstr ""

#: text.c:185
#, c-format
msgid "Can't handle URLs of length over %d\n"
msgstr "不能处理长度超过 %d 的URLs\n"

#: text.c:190
#, c-format
msgid "Initializing download: %s\n"
msgstr "初始化下载: %s\n"

#: text.c:197
#, c-format
msgid "Doing search...\n"
msgstr "进行搜索中...\n"

#: text.c:201
#, c-format
msgid "File not found\n"
msgstr "文件找不到\n"

#: text.c:205
#, c-format
msgid "Testing speeds, this can take a while...\n"
msgstr "c测试速度，这可能有点费时……\n"

#: text.c:210
#, c-format
msgid "%i usable servers found, will use these URLs:\n"
msgstr "%i 可用的服务器没有找到，将使用这些 URLs ：\n"

#: text.c:269
#, c-format
msgid "Filename too long!\n"
msgstr ""

#: text.c:281
#, c-format
msgid "No state file, cannot resume!\n"
msgstr "没有状态文件，无法恢复！\n"

#: text.c:286
#, c-format
msgid "State file found, but no downloaded data. Starting from scratch.\n"
msgstr "找到状态文件，但没有已下载数据。重新开始。\n"

#: text.c:417
#, c-format
msgid ""
"\n"
"Downloaded %s in %s. (%.2f KB/s)\n"
msgstr ""
"\n"
"%s 已下载，用时 %s。（%.2f 千字节/秒）\n"

#: text.c:439
#, fuzzy, c-format
msgid "%lld byte"
msgstr "%i 字节"

#: text.c:441
#, fuzzy, c-format
msgid "%lld bytes"
msgstr "%i 字节"

#: text.c:443
#, c-format
msgid "%.1f kilobytes"
msgstr "%.1f 千字节"

#: text.c:445
#, c-format
msgid "%.1f megabytes"
msgstr "%.1f 兆字节"

#: text.c:454
#, c-format
msgid "%i second"
msgstr "%i 秒"

#: text.c:456
#, c-format
msgid "%i seconds"
msgstr "%i 秒"

#: text.c:458
#, c-format
msgid "%i:%02i seconds"
msgstr "%i:%02i 秒"

#: text.c:460
#, c-format
msgid "%i:%02i:%02i seconds"
msgstr "%i:%02i:%02i 秒"

#: text.c:540
#, fuzzy, c-format
msgid ""
"Usage: axel [options] url1 [url2] [url...]\n"
"\n"
"-s x\tSpecify maximum speed (bytes per second)\n"
"-n x\tSpecify maximum number of connections\n"
"-o f\tSpecify local output file\n"
"-S [x]\tSearch for mirrors and download from x servers\n"
"-H x\tAdd header string\n"
"-U x\tSet user agent\n"
"-N\tJust don't use any proxy server\n"
"-q\tLeave stdout alone\n"
"-v\tMore status information\n"
"-a\tAlternate progress indicator\n"
"-h\tThis information\n"
"-V\tVersion information\n"
"\n"
"Visit http://axel.alioth.debian.org/ to report bugs\n"
msgstr ""
"用法: axel [选项] 地址1 [地址2] [地址...]\n"
"\n"
"-s x\t指定最大速率（字节 / 秒）\n"
"-n x\t指定最大连接数\n"
"-o f\t指定本地输出文件\n"
"-S [x]\t搜索镜像并从 X 服务器下载\n"
"-N\t不使用任何代理服务器\n"
"-q\t使用输出简单信息模式\n"
"-v\t更多状态信息\n"
"-a\t文本式进度指示器\n"
"-h\t帮助信息\n"
"-V\t版本信息\n"
"\n"
"请向 <EMAIL> 提交翻译错误，请向 http://axel.alioth.debian.org/ 提"
"交程序漏洞\n"

#: text.c:557
#, fuzzy, c-format
msgid ""
"Usage: axel [options] url1 [url2] [url...]\n"
"\n"
"--max-speed=x\t\t-s x\tSpecify maximum speed (bytes per second)\n"
"--num-connections=x\t-n x\tSpecify maximum number of connections\n"
"--output=f\t\t-o f\tSpecify local output file\n"
"--search[=x]\t\t-S [x]\tSearch for mirrors and download from x servers\n"
"--header=x\t\t-H x\tAdd header string\n"
"--user-agent=x\t\t-U x\tSet user agent\n"
"--no-proxy\t\t-N\tJust don't use any proxy server\n"
"--quiet\t\t\t-q\tLeave stdout alone\n"
"--verbose\t\t-v\tMore status information\n"
"--alternate\t\t-a\tAlternate progress indicator\n"
"--help\t\t\t-h\tThis information\n"
"--version\t\t-V\tVersion information\n"
"\n"
"Visit http://axel.alioth.debian.org/ to report bugs\n"
msgstr ""
"用法: axel [选项] 地址1 [地址2] [地址...]\n"
"\n"
"--max-speed=x\t\t-s x\t指定最大速率（字节 / 秒）\n"
"--num-connections=x\t-n x\t指定最大连接数\n"
"--output=f\t\t-o f\t指定本地输出文件\n"
"--search[=x]\t\t-S [x]\t搜索镜像并从 X 服务器下载\n"
"--no-proxy\t\t-N\t不使用任何代理服务器\n"
"--quiet\t\t\t-q\t使用输出简单信息模式\n"
"--verbose\t\t-v\t更多状态信息\n"
"--alternate\t\t-a\t文本式进度指示器\n"
"--help\t\t\t-h\t帮助信息\n"
"--version\t\t-V\t版本信息\n"
"\n"
"请向 <EMAIL> 提交翻译错误，请向 http://axel.alioth.debian.org/ 提"
"交程序漏洞\n"

#: text.c:578
#, c-format
msgid "Axel version %s (%s)\n"
msgstr "Axel 版本 %s (%s)\n"
