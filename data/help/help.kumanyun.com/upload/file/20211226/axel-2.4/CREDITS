An not-quite-sorted list of people who helped somehow:

- <PERSON> <<EMAIL>>
  Bug triage and patches to fix bugs

- bbbush <<EMAIL>>
  RPM spec file

- newhren <<EMAIL>>
  Russian translation

- <PERSON> <<EMAIL>>
  Custom Header Support

- <PERSON> <dsturn<PERSON>@gmail.com>
  Large file support

- <PERSON> <<EMAIL>>
  German translation.

- <PERSON> <<EMAIL>>
  Requested the human-readable time/size values.
  Advertising in the Dutch Linux User's Manual. (http://2mypage.cjb.net/)

- <PERSON><PERSON> <<EMAIL>>
  Early tester.

- <PERSON> <<EMAIL>>
  For having an iMac with a very badly-behaving FTP server...

- <PERSON> <<EMAIL>>
  For creating the initial native RPM packages instead of my alienated
  .debs.

- <PERSON> <<EMAIL>>
  For patching the program to make it work on FreeBSD and Darwin.
  For finding some very stupid bugs.

- <PERSON><PERSON><PERSON> <<EMAIL>>
  For adding the finish_time feature. It's not yet in the user interface,
  though...
  For writing axelq.

- <PERSON> <<EMAIL>>
  For being a very good beta tester.
  For creating axel-kapt.

- <PERSON> A <<EMAIL>>
  For some testing and for the new (multi-URL) syntax idea.

- Sebastian Ritterbusch <<EMAIL>>
  For some interesting ideas for the new versions.
  For writing the alternate progress indicator.
