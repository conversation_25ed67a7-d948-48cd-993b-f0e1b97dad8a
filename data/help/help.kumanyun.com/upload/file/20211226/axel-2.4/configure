#!/bin/sh

###########################
##  Configurer for Axel  ##
##                       ##
## Copyright 2001 Lintux ##
###########################

prefix='/usr/local'
bindir='$prefix/bin'
etcdir='$prefix/etc'
sharedir='$prefix/share'
mandir='$sharedir/man'
locale='$sharedir/locale'

i18n=1
debug=0
strip=1

arch=`uname -s`

while [ -n "$1" ]; do
	e="`expr "$1" : '--\(.*=.*\)'`"
	if [ -z "$e" ]; then
		cat<<EOF
Axel configure

Usage: $0 [OPTIONS]

Option		Description				Default

--prefix=...	Directories to put files in		$prefix
--bindir=...						$bindir
--etcdir=...						$etcdir
--mandir=...						$mandir
--locale=...						$locale

--i18n=0/1	Disable/enable internationalization	$i18n
--debug=0/1	Disable/enable debugging		$debug
--strip=0/1	Disable/enable binary stripping		$strip
EOF
		exit;
	fi
	
	keyname=$(expr "$e" : '\(.*\)=.*' | sed 's/[^a-z0-9_]/_/g')
	value=$(expr "$e" : '.*=\(.*\)' | sed "s/'/_/g")
	
	eval "$keyname='$value'"
	shift;
done

# Expand $prefix
bindir=`eval echo $bindir`
etcdir=`eval echo $etcdir`
sharedir=`eval echo $sharedir`
mandir=`eval echo $mandir`
locale=`eval echo $locale`
if test "$etcdir" = "/usr/etc"; then
	# FHS explicitely forbids /usr/etc
	etcdir='/etc'
fi

cat<<EOF>Makefile.settings
## Axel settings, generated by configure
PREFIX=$prefix
BINDIR=$bindir
ETCDIR=$etcdir
SHAREDIR=$sharedir
MANDIR=$mandir
LOCALE=$locale

OUTFILE=axel
ARCH=$arch

DESTDIR=
LFLAGS=

EOF

cat<<EOF>config.h
/* Axel settings, generated by configure */
#define _REENTRANT
#define _THREAD_SAFE
#define ETCDIR "$etcdir"
#define LOCALE "$locale"
#define ARCH "$arch"

EOF

if [ "$i18n" = "1" ]; then
	if type msgfmt > /dev/null 2> /dev/null; then :;else
		echo 'WARNING: Internationalization disabled, you don'\''t have the necessary files'
		echo '         installed.'
		echo ''
		i18n=0;
	fi;
fi

echo "CFLAGS=-D_LARGEFILE_SOURCE -D_FILE_OFFSET_BITS=64 -Os ${CFLAGS}" >> Makefile.settings
if [ "$debug" = "1" ]; then
	echo 'CFLAGS+=-g' >> Makefile.settings
	echo 'DEBUG=1' >> Makefile.settings
	echo '#define DEBUG' >> config.h;
fi

if [ "$i18n" = "1" ]; then
	echo 'I18N=1' >> Makefile.settings
	echo '#define I18N' >> config.h
	if cat /usr/local/include/libintl.h > /dev/null 2> /dev/null; then
		echo 'CFLAGS+=-I/usr/local/include' >> Makefile.settings
		echo 'LFLAGS+=-L/usr/local/lib' >> Makefile.settings;
	fi;
fi

if [ "${CC}" != "" ]; then
	echo "CC=${CC}" >> Makefile.settings;
elif type gcc > /dev/null 2> /dev/null; then
	echo "CC=gcc" >> Makefile.settings;
elif type cc > /dev/null 2> /dev/null; then
	echo "CC=cc" >> Makefile.settings;
else
	echo 'Cannot find a C compiler, aborting.'
	exit 1;
fi

if [ "$strip" = 0 ]; then
	echo "STRIP=\# skip strip" >> Makefile.settings;
else
	echo 'The strip option is enabled. This should not be a problem usually, but on some'
	echo 'systems it breaks stuff.'
	echo
	if [ "$debug" = 1 ]; then
		echo 'Stripping binaries does not make sense when debugging. Stripping disabled.'
		echo
		echo 'STRIP=\# skip strip' >> Makefile.settings
		strip=0;
	elif type strip > /dev/null 2> /dev/null; then
		echo "STRIP=strip" >> Makefile.settings;
	elif /bin/test -x /usr/ccs/bin/strip; then
		echo "STRIP=/usr/ccs/bin/strip" >> Makefile.settings;
	else
		echo 'No strip utility found, cannot remove unnecessary parts from executable.'
		echo ''
		echo 'STRIP=\# skip strip' >> Makefile.settings
		strip=0;
	fi;
fi

case "$arch" in
FreeBSD )
	echo '#define NOGETOPTLONG' >> config.h
	echo 'LFLAGS+=-pthread' >> Makefile.settings
	if [ "$i18n" = "1" ]; then
		echo 'LFLAGS+=-lintl' >> Makefile.settings;
	fi
	echo 'Please keep in mind that you need GNU make to make Axel!'
	echo ''
;;
OpenBSD )
	echo '#define NOGETOPTLONG' >> config.h
	echo 'LFLAGS+=-pthread' >> Makefile.settings
	if [ "$i18n" = "1" ]; then
		echo 'LFLAGS+=-lintl' >> Makefile.settings;
	fi
	echo 'Please keep in mind that you need GNU make to make Axel!'
	echo ''
;;
NetBSD )
	echo 'WARNING: NetBSD not tested! Using OpenBSD settings.'
	echo '         Please send me your results so I can update this!'
	echo ''
	echo '#define NOGETOPTLONG' >> config.h
	echo 'LFLAGS+=-pthread' >> Makefile.settings
	if [ "$i18n" = "1" ]; then
		echo 'LFLAGS+=-lintl' >> Makefile.settings;
	fi
	echo 'Please keep in mind that you need GNU make to make Axel!'
	echo ''
;;
Darwin )
	echo '#define NOGETOPTLONG' >> config.h
	echo 'LFLAGS+=-lpthread' >> Makefile.settings
	if [ "$i18n" = "1" ]; then
		echo 'LFLAGS+=-lintl' >> Makefile.settings;
	fi
	echo '#define DARWIN' >> config.h
	echo 'Please keep in mind that you need GNU make to make Axel!'
	echo ''
;;
Linux | GNU/kFreeBSD)
	echo 'LFLAGS+=-lpthread' >> Makefile.settings
;;
SunOS )
	echo '#define NOGETOPTLONG' >> config.h
	echo '#define BSD_COMP' >> config.h
	echo 'LFLAGS+=-lpthread -lsocket -lnsl' >> Makefile.settings
	if [ "$i18n" = "1" ]; then
		echo 'LFLAGS+=-lintl' >> Makefile.settings;
	fi
	echo 'Please keep in mind that you need GNU make to make Axel!'
	echo ''
;;
CYGWIN_* )
	echo 'LFLAGS+=-lpthread' >> Makefile.settings
	if [ "$i18n" = "1" ]; then
		echo 'LFLAGS+=-lintl' >> Makefile.settings;
	fi
	echo 'OUTFILE=axel.exe' >> Makefile.settings
;;
* )
	echo 'LFLAGS+=-lpthread' >> Makefile.settings
	echo '#define NOGETOPTLONG' >> config.h
	if [ "$i18n" = "1" ]; then
		echo 'LFLAGS+=-lintl' >> Makefile.settings;
	fi
	echo 'WARNING: This architecture is unknown!'
	echo ''
	echo 'That does not mean Axel will not work, it just means I'\''ve never had the chance'
	echo 'to test Axel on it. It'\''d be a great help if you could send me more information'
	echo 'about your platform so I can add it to the build tools.'
	echo 'You can try to build the program now, if you wish, this default setup might'
	echo 'just work.'
	echo ''
;;
esac

echo 'Configuration done:'
if [ "$i18n" = "1" ]; then
	echo '  Internationalization enabled.';
else
	echo '  Internationalization disabled.';
fi
if [ "$debug" = "1" ]; then
	echo '  Debugging enabled.';
else
	echo '  Debugging disabled.';
fi
if [ "$strip" = "1" ]; then
	echo '  Binary stripping enabled.';
else
	echo '  Binary stripping disabled.';
fi
