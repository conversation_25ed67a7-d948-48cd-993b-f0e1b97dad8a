.\"
.\"man-page for axel-kapt
.\"
.\"Derived from axel, which is derived from the man-page example in the
.\"wonderful book called Beginning "Linux Programming", written by <PERSON>
.\"<PERSON> and <PERSON>.
.\"
.TH AXEL-KAPT 1

.SH NAME
\fBaxel\-kapt\fP \- Axel front\-end

.SH SYNOPSIS
.B axel\-kapt
\fIurl1\fP [\fIurl2\fP] [\fIurl...\fP]

.SH DESCRIPTION
Axel is a program that downloads a file from a FTP or HTTP server through
multiple connection, each connection downloads its own part of the file.
This program is a KDE front\-end to Axel.

.SH CONFIGURATION
The program is a Python script which generates a Kaptain grammar on the fly.

.SH "SEE ALSO"
axel(1)

.SH COPYRIGHT
Axel\-kapt is Copyright 2001 <PERSON>.

Axel is Copyright 2001 Wil<PERSON>.

.SH AUTHORS
<PERSON> (<EMAIL>)
