.\"
.\"man-page for <PERSON>
.\"
.\"Derived from the man-page example in the wonderful book called Beginning
.\"Linux Programming, written by <PERSON> and <PERSON>
.\"
.TH AXEL 1

.SH NAME
\fBAxel\fP \- A light download accelerator for Linux.

.SH SYNOPSIS
.B axel
[\fIOPTIONS\fP] \fIurl1\fP [\fIurl2\fP] [\fIurl...\fP]

.SH DESCRIPTION
Axel is a program that downloads a file from a FTP or HTTP server through
multiple connection, each connection downloads its own part of the file.

Unlike most other programs, <PERSON> downloads all the data directly to the
destination file, using one single thread. It just saves some time at the
end because the program doesn't have to concatenate all the downloaded
parts.

.SH OPTIONS
.PP
One argument is required, the URL to the file you want to download. When
downloading from FTP, the filename may contain wildcards and the program
will try to resolve the full filename. Multiple URL's can be specified
as well and the program will use all those URL's for the download. Please
note that the program does not check whether the files are equal.

.PP
Other options:

.TP
\fB\-\-max\-speed=x\fP, \fB\-s\ x\fP
You can specify a speed (bytes per second) here and <PERSON> will try
to keep the average speed around this speed. Useful if you don't want
the program to suck up all of your bandwidth.

.TP
\fB\-\-num\-connections=x\fP, \fB\-n\ x\fP
You can specify an alternative number of connections here.

.TP
\fB\-\-output=x\fP, \fB\-o\ x\fP
Downloaded data will be put in a local file with the same name,
unless you specify a different name using this option. You can
specify a directory as well, the program will append the filename.

.TP
\fB\-\-search[=x]\fP, \fB-S[x]\fP
Axel can do a search for mirrors using the filesearching.com search
engine. This search will be done if you use this option. You can specify how
many different mirrors should be used for the download as well.

The search for mirrors can be time\-consuming because the program tests
every server's speed, and it checks whether the file's still available.

.TP
\fB\-\-no\-proxy\fP, \fB\-N\fP
Don't use any proxy server to download the file. Not possible when a
transparent proxy is active somewhere, of course.

.TP
\fB\-\-verbose\fP
If you want to see more status messages, you can use this option. Use it
more than once if you want to see more.

.TP
\fB\-\-quiet\fP, \fB-q\fP
No output to stdout.

.TP
\fB\-\-alternate\fP, \fB-a\fP
This will show an alternate progress indicator. A bar displays the progress
and status of the different threads, along with current speed and an
estimate for the remaining download time.

.TP
\fB\-\-header=x\fP, \fB\-H\ x\fP
Add an additional HTTP header. This option should be in the form "Header:
Value". See RFC 2616 section 4.2 and 14 for details on the format and
standardized headers.

.TP
\fB\-\-user-agent=x\fP, \fB\-U\ x\fP
Set the HTTP user agent to use. Some websites serve different content based upon
this parameter. The default value will include "Axel", its version and the
platform.

.TP
\fB\-\-help\fP, \fB\-h\fP
A brief summary of all the options.

.TP
\fB\-\-version\fP, \fB\-V\fP
Get version information.

.SH NOTE
Long (double dash) options are supported only if your platform knows about
the getopt_long call. If it does not (like *BSD), only the short options can
be used.

.SH RETURN VALUE
The program returns 0 when the download was succesful, 1 if something really
went wrong and 2 if the download was interrupted. If something else comes back,
it must be a bug..

.SH EXAMPLES
.nf
axel ftp://ftp.{be,nl,uk,de}.kernel.org/pub/linux/kernel/v2.4/linux-2.4.17.tar.bz2
.fi

This will use the Belgian, Dutch, English and German kernel.org mirrors to
download a Linux 2.4.17 kernel image.

.nf
axel \-S4 ftp://ftp.kernel.org/pub/linux/kernel/v2.4/linux-2.4.17.tar.bz2
.fi

This will do a search for the linux-2.4.17.tar.bz2 file on filesearching.com
and it'll use the four (if possible) fastest mirrors for the download.
(Possibly including ftp.kernel.org)

(Of course, the commands are a single line, but they're too long to fit on
one line in this page.)

.SH FILES
.PP
\fI/etc/axelrc\fP System-wide configuration file. Note that development versions
place this file in /usr/local/etc.
.PP
\fI~/.axelrc\fP Personal configuration file
.PP
These files are not documented in a man\-page, but the example file which
comes with the program contains enough information, I hope. The position
of the system-wide configuration file might be different.

.SH COPYRIGHT
Axel is Copyright 2001-2002 Wilmer van der Gaast.

.SH BUGS
Please report bugs at https://alioth.debian.org/tracker/?group_id=100070&atid=413085.

.SH AUTHORS
Wilmer van der Gaast. <<EMAIL>>
