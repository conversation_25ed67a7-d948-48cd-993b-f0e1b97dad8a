<?php
/**
 * 商家旧数据转换脚本，该脚本用于将商家入驻订单数据由旧版本转换为新版本
 * 主要有两个功能：
 * 1. 将所有商家入驻的套餐类型转为自选，并将套餐中的内容更新为自选数据；
 * 2. 提取入驻订单中的套餐内容，转入o_name,o_times,o_package中，用于开通记录接口使用；
 * 
 * 注意：转换前，请先备份好数据，以免转换出错导致数据异常！
 * 
 * 操作步骤：
 * 1. 进入后台商店，校验并同步数据库；
 * 2. 将本文件上传至网站根目录；
 * 3. 在浏览器中通过域名访问该文件，即可开始转换；
 * 4. 转换完成后，请删除该文件；
 * 5. 进入后台商店，校验并同步文件；
 */ 
set_time_limit(0);  //运行时长

//加载系统核心文件
require_once(dirname(__FILE__)."/include/common.inc.php");

//加载商家配置文件
include HUONIAOINC . "/config/business.inc.php";

$businessPackage = $businessPackage ? unserialize($businessPackage) : array();

if(!$businessPackage) die('商家入驻套餐错误，转换失败！');

//进行第一个功能，将所有商家入驻的套餐类型转为自选，并将套餐中的内容更新为自选数据；
//先查询没有转换过的商家，package_list为空说明没有转换
$sql = $dsql->SetQuery("SELECT * FROM `#@__business_list` WHERE `package_list` = '' ORDER BY `id` ASC");
$ret = $dsql->dsqlOper($sql, "results");
if($ret){

    foreach($ret as $key => $val){

        $_id = (int)$val['id'];  //商家ID
        $_type = (int)$val['type'];  //当前套餐
        $_expired = (int)$val['expired'];  //过期时间

        //已开套餐内容
        $busiPackage = $val['package'] ? unserialize($val['package']) : array();

        //已开套餐的套餐类型
        $package_package = $busiPackage ? (int)$busiPackage['package'] : '';

        //如果type和package不一致，以package为准
        if($package_package != '' && $package_package != $_type){
            $_type = $package_package;
        }

        //已开套餐的自选内容
        $package_item = $busiPackage ? $busiPackage['item'] : array();

        //已开套餐的过期时间
        $package_expired = $busiPackage ? (int)$busiPackage['expired'] : 0;

        if(!$_expired && $package_expired){
            $_expired = $package_expired;
        }

        //如果套餐内容为空，并且过期时间也为0，说明该商家没有开通任何套餐，则跳过
        if(!$_expired && !$busiPackage) continue;

        $newItem = array();

        //记录已经开通的特权，用于后台的检索
        $package_list = array();

        //更新自选套餐的package_list
        if($_type == -1){

            //如果已经开通过自选套餐
            if ($package_item) {
                foreach ($package_item as $key => $value) {
                    array_push($package_list, $value['name']);
                }

                $package_list = join(',', $package_list);

                $sql = $dsql->SetQuery("UPDATE `#@__business_list` SET `package_list` = '$package_list' WHERE `id` = $_id");
                $dsql->dsqlOper($sql, "update");
            } 

        }
        else{

            //套餐中的内容
            $_package_item = $businessPackage[$_type]['list'];
            $itemArr = explode(',', $_package_item);

            //如果已经开通过自选套餐
            if ($package_item) {
                foreach ($package_item as $key => $value) {
                    if (in_array($value['name'], $itemArr)) {
                        array_push($newItem, array(
                            'name' => $value['name'],
                            'expired' => (int)$value['expired']
                        ));

                        array_splice($itemArr, array_search($value['name'], $itemArr), 1);  //删除已经存在的内容
                    } else {
                        array_push($newItem, $value);
                    }

                    array_push($package_list, $value['name']);
                }

                //如果还有新的套餐内容，继续累加
                if ($itemArr) {
                    foreach ($itemArr as $key => $value) {
                        array_push($newItem, array(
                            'name' => $value,
                            'expired' => $_expired
                        ));

                        array_push($package_list, $value);
                    }
                }
            } 
            
            //没有开通过，直接用当前选择的自选套餐
            else {
                foreach ($itemArr as $key => $value) {
                    array_push($newItem, array(
                        'name' => $value,
                        'expired' => $_expired
                    ));

                    array_push($package_list, $value);
                }
            }

            $packageContent = array(
                'package' => '-1',  //套餐ID
                'expired' => 0,   //保持原有过期时间
                'item' => $newItem       //最新的自选套餐内容
            );

            $packageContent = serialize($packageContent);
            $package_list = join(',', $package_list);

            $sql = $dsql->SetQuery("UPDATE `#@__business_list` SET `type` = '-1', `package` = '$packageContent', `package_list` = '$package_list' WHERE `id` = $_id");
            $dsql->dsqlOper($sql, "update");

        }

    }

}


//商家特权 & 行业特权
$businessPrivilege = $businessPrivilege ? unserialize($businessPrivilege) : array();
$businessStore = $businessStore ? unserialize($businessStore) : array();

if($businessPrivilege && $businessStore){

    //进行第二个功能，提取入驻订单中的套餐内容，转入o_name,o_times,o_package中，用于开通记录接口使用；
    $sql = $dsql->SetQuery("SELECT * FROM `#@__business_order` WHERE `state` = 1 AND `name` = '' AND `o_name` = '' ORDER BY `id` ASC");
    $ret = $dsql->dsqlOper($sql, "results");
    if($ret){
        foreach($ret as $key => $val){

            $_id = (int)$val['id'];

            //套餐内容
            $busiPackage = $val['package'] ? unserialize($val['package']) : array();

            if(!$busiPackage) continue;

            //套餐类型
            $package_package = $busiPackage ? (int)$busiPackage['package'] : '';

            //开通时长
            $package_month = $busiPackage ? (int)$busiPackage['month'] : '';

            //自选套餐内容
            $package_item = $busiPackage ? $busiPackage['packageItem'] : '';

            $o_name = $package_package == -1 ? '自选套餐' : $businessPackage[$_type]['title'];
            $o_times = $package_month ? $package_month . '个月' : '未知';

            //查询套餐内容
            $package_list = '';
            if($package_package == -1){
                $package_list = $package_item;
            }else{
                $package_list = $businessPackage[$_type]['list'];
            }

            $modules = array();
            $packageItem = explode(',', $package_list);
            if ($packageItem) {
                foreach ($packageItem as $k => $v) {
                    if ($businessPrivilege[$v]) {
                        $modules[] = $businessPrivilege[$v]['title'];
                    }
                    if ($businessStore[$v]) {
                        $modules[] = $businessStore[$v]['title'];
                    }
                }
            }

            if($package_package == -1 && !$modules){
                $modules = explode(',', $package_item);
            }

            if($modules){
                $modules = join(',', $modules);
                
                $sql = $dsql->SetQuery("UPDATE `#@__business_order` SET `o_name` = '$o_name', `o_times` = '$o_times', `o_package` = '$modules' WHERE `id` = " . $_id);
                $dsql->dsqlOper($sql, "update");
            }


        }
    }
}

else{
    die('商家特权和行业特权配置错误，转换失败！');
}


echo '<h1>转换完成，请删除该文件！</h1>';