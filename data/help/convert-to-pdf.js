const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

async function convertHtmlToPdf() {
    const browser = await puppeteer.launch({
        headless: true,
        args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    const page = await browser.newPage();
    
    // 要转换的HTML文件列表
    const htmlFiles = [
        'help-240.html',
        'help-239.html', 
        'help-278.html',
        'help-241.html'
    ];
    
    const baseDir = '/Users/<USER>/Desktop/Trea ai /data/help/help.kumanyun.com';
    
    console.log('开始转换HTML文件为PDF...');
    
    for (const htmlFile of htmlFiles) {
        try {
            const htmlPath = path.join(baseDir, htmlFile);
            const pdfPath = path.join(baseDir, htmlFile.replace('.html', '.pdf'));
            
            // 检查HTML文件是否存在
            if (!fs.existsSync(htmlPath)) {
                console.log(`❌ 文件不存在: ${htmlPath}`);
                continue;
            }
            
            console.log(`🔄 正在转换: ${htmlFile}`);
            
            // 加载HTML文件
            await page.goto(`file://${htmlPath}`, {
                waitUntil: 'networkidle0',
                timeout: 30000
            });
            
            // 生成PDF
            await page.pdf({
                path: pdfPath,
                format: 'A4',
                printBackground: true,
                margin: {
                    top: '20px',
                    right: '20px',
                    bottom: '20px',
                    left: '20px'
                }
            });
            
            console.log(`✅ 转换完成: ${htmlFile} -> ${htmlFile.replace('.html', '.pdf')}`);
            
        } catch (error) {
            console.error(`❌ 转换失败 ${htmlFile}:`, error.message);
        }
    }
    
    await browser.close();
    console.log('🎉 所有转换任务完成！');
}

// 执行转换
convertHtmlToPdf().catch(console.error);