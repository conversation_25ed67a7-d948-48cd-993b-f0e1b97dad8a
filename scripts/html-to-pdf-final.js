const { chromium } = require('playwright');
const fs = require('fs');
const path = require('path');
const { JSDOM } = require('jsdom');

// 配置常量
const CONFIG = {
    baseDir: '/Users/<USER>/Desktop/Trea ai /data/help/help.kumanyun.com',
    outputDir: '/Users/<USER>/Desktop/Trea ai /data/help/pdf-final',
    mainFiles: [
        'help-4.html',  // 网站后台
        'help-5.html',  // 功能详解
        'help-7.html'   // 二次开发
    ],
    pdfOptions: {
        format: 'A4',
        printBackground: true,
        margin: {
            top: '20px',
            right: '20px',
            bottom: '20px',
            left: '20px'
        }
    },
    // 批量处理配置
    batchSize: 3,  // 减少批次大小，因为图片会增加内存使用
    delayBetweenFiles: 2000,  // 增加延迟
    delayBetweenBatches: 5000,
    maxRetries: 2
};

/**
 * 获取图片的MIME类型
 */
function getImageMimeType(filePath) {
    const ext = path.extname(filePath).toLowerCase();
    const mimeTypes = {
        '.png': 'image/png',
        '.jpg': 'image/jpeg',
        '.jpeg': 'image/jpeg',
        '.gif': 'image/gif',
        '.webp': 'image/webp',
        '.svg': 'image/svg+xml'
    };
    return mimeTypes[ext] || 'image/png';
}

/**
 * 将图片转换为base64 data URL
 */
function imageToBase64(imagePath) {
    try {
        const imageBuffer = fs.readFileSync(imagePath);
        const mimeType = getImageMimeType(imagePath);
        const base64 = imageBuffer.toString('base64');
        return `data:${mimeType};base64,${base64}`;
    } catch (error) {
        return null;
    }
}

/**
 * 从HTML文件中提取所有相关链接
 */
function extractLinksFromHtml(htmlFilePath) {
    try {
        const htmlContent = fs.readFileSync(htmlFilePath, 'utf8');
        const dom = new JSDOM(htmlContent);
        const document = dom.window.document;
        
        const links = [];
        const menuLinks = document.querySelectorAll('.menu-list a[href^="help-"]');
        
        menuLinks.forEach(link => {
            const href = link.getAttribute('href');
            const title = link.textContent.trim();
            
            if (href && href.endsWith('.html')) {
                links.push({
                    href: href,
                    title: title,
                    type: link.classList.contains('article') ? 'article' : 'category'
                });
            }
        });
        
        return links;
    } catch (error) {
        console.error(`提取链接失败: ${htmlFilePath}`, error);
        return [];
    }
}

/**
 * 获取主文件标题
 */
function getMainFileTitle(filename) {
    const titles = {
        'help-4.html': '网站后台',
        'help-5.html': '功能详解', 
        'help-7.html': '二次开发'
    };
    return titles[filename] || filename;
}

/**
 * 获取所有相关的HTML文件列表
 */
function getAllRelatedFiles(mainFile) {
    const mainFilePath = path.join(CONFIG.baseDir, mainFile);
    const links = extractLinksFromHtml(mainFilePath);
    
    const files = [];
    
    // 添加主文件
    files.push({
        filename: mainFile,
        path: path.join(CONFIG.baseDir, mainFile),
        title: getMainFileTitle(mainFile),
        type: 'main'
    });
    
    // 添加所有子页面文件
    links.forEach((link, index) => {
        const filePath = path.join(CONFIG.baseDir, link.href);
        if (fs.existsSync(filePath)) {
            files.push({
                filename: link.href,
                path: filePath,
                title: link.title,
                type: link.type,
                index: index + 1
            });
        }
    });
    
    return files;
}

/**
 * 提取并修复HTML内容，将图片转换为base64
 */
function extractAndFixContent(htmlFilePath) {
    try {
        const htmlContent = fs.readFileSync(htmlFilePath, 'utf8');
        const dom = new JSDOM(htmlContent);
        const document = dom.window.document;
        
        // 提取页面标题
        const titleElement = document.querySelector('.content h3.title');
        const pageTitle = titleElement ? titleElement.textContent.trim() : 
                         path.basename(htmlFilePath, '.html');
        
        // 提取面包屑导航
        const breadElement = document.querySelector('.bread');
        const breadcrumb = breadElement ? breadElement.textContent.trim() : '';
        
        // 提取主要内容区域
        const contentElement = document.querySelector('.content');
        if (!contentElement) {
            throw new Error('未找到内容区域');
        }
        
        // 处理图片：转换为base64
        const images = contentElement.querySelectorAll('img');
        let validImageCount = 0;
        
        images.forEach((img, index) => {
            const src = img.getAttribute('src');
            if (src && src.startsWith('upload/')) {
                const imagePath = path.join(CONFIG.baseDir, src);
                
                if (fs.existsSync(imagePath)) {
                    const base64Url = imageToBase64(imagePath);
                    if (base64Url) {
                        img.setAttribute('src', base64Url);
                        if (!img.getAttribute('alt')) {
                            img.setAttribute('alt', `图片 ${index + 1}`);
                        }
                        validImageCount++;
                    } else {
                        img.remove();
                    }
                } else {
                    img.remove();
                }
            }
        });
        
        // 创建完整的HTML文档
        const fullHtml = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${pageTitle}</title>
    <style>
        body {
            font-family: "Microsoft YaHei", "PingFang SC", "Helvetica Neue", Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #fff;
        }
        .breadcrumb {
            font-size: 14px;
            color: #666;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }
        .title {
            font-size: 24px;
            font-weight: bold;
            color: #333;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #007bff;
        }
        .content {
            font-size: 15px;
            line-height: 1.8;
        }
        .content img {
            max-width: 100% !important;
            height: auto !important;
            display: block;
            margin: 15px auto;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            page-break-inside: avoid;
        }
        .content p {
            margin: 10px 0;
            page-break-inside: avoid;
        }
        .content strong {
            color: #007bff;
            font-weight: bold;
        }
        .content a {
            color: #007bff;
            text-decoration: none;
        }
        .lake-content {
            margin: 15px 0;
        }
        .ne-p {
            margin: 8px 0;
            line-height: 1.6;
        }
        @media print {
            body { margin: 0; }
            .content img { 
                max-width: 100% !important;
                page-break-inside: avoid;
            }
        }
    </style>
</head>
<body>
    ${breadcrumb ? `<div class="breadcrumb">${breadcrumb}</div>` : ''}
    <h1 class="title">${pageTitle}</h1>
    <div class="content">
        ${contentElement.innerHTML}
    </div>
</body>
</html>`;
        
        return {
            title: pageTitle,
            breadcrumb: breadcrumb,
            content: fullHtml,
            imageCount: validImageCount,
            hasImages: validImageCount > 0
        };
        
    } catch (error) {
        console.error(`提取内容失败: ${htmlFilePath}`, error);
        return null;
    }
}

/**
 * 转换单个HTML文件为PDF
 */
async function convertHtmlToPdf(fileInfo, outputPath, browser, retryCount = 0) {
    try {
        const page = await browser.newPage();

        // 设置视口大小
        await page.setViewportSize({ width: 1200, height: 800 });

        // 提取和修复内容
        const contentData = extractAndFixContent(fileInfo.path);
        if (!contentData) {
            throw new Error('无法提取页面内容');
        }

        // 设置页面内容
        await page.setContent(contentData.content, {
            waitUntil: 'networkidle',
            timeout: 30000
        });

        // 等待页面渲染完成
        await page.waitForTimeout(1000);

        // 生成PDF
        await page.pdf({
            path: outputPath,
            ...CONFIG.pdfOptions
        });

        await page.close();

        // 检查文件大小
        const stats = fs.statSync(outputPath);
        const fileSizeMB = (stats.size / 1024 / 1024).toFixed(2);

        return {
            success: true,
            fileSize: fileSizeMB,
            title: contentData.title,
            imageCount: contentData.imageCount
        };

    } catch (error) {
        if (retryCount < CONFIG.maxRetries) {
            console.log(`  重试 ${retryCount + 1}/${CONFIG.maxRetries}...`);
            await new Promise(resolve => setTimeout(resolve, 2000));
            return await convertHtmlToPdf(fileInfo, outputPath, browser, retryCount + 1);
        }
        throw error;
    }
}

/**
 * 批量转换单个主文档的所有相关文件
 */
async function convertMainDocument(mainFile, browser) {
    const mainTitle = getMainFileTitle(mainFile);
    console.log(`\n开始处理: ${mainTitle} (${mainFile})`);
    console.log('='.repeat(50));

    // 创建输出目录
    const outputDir = path.join(CONFIG.outputDir, mainTitle);
    if (!fs.existsSync(outputDir)) {
        fs.mkdirSync(outputDir, { recursive: true });
    }

    // 获取所有相关文件
    const files = getAllRelatedFiles(mainFile);
    console.log(`找到 ${files.length} 个相关文件`);

    let successCount = 0;
    let failCount = 0;
    const results = [];

    // 分批处理文件
    for (let i = 0; i < files.length; i += CONFIG.batchSize) {
        const batch = files.slice(i, i + CONFIG.batchSize);
        const batchNum = Math.floor(i / CONFIG.batchSize) + 1;
        const totalBatches = Math.ceil(files.length / CONFIG.batchSize);

        console.log(`\n批次 ${batchNum}/${totalBatches} (${batch.length} 个文件):`);

        // 处理当前批次的每个文件
        for (let j = 0; j < batch.length; j++) {
            const file = batch[j];
            const fileIndex = i + j + 1;
            const outputFileName = `${String(fileIndex).padStart(3, '0')}_${file.title.replace(/[\/\\:*?"<>|]/g, '_')}.pdf`;
            const outputPath = path.join(outputDir, outputFileName);

            try {
                console.log(`[${fileIndex}/${files.length}] ${file.title}`);
                const result = await convertHtmlToPdf(file, outputPath, browser);
                console.log(`  ✓ 成功 (${result.fileSize} MB, ${result.imageCount} 张图片)`);

                results.push({
                    file: file.filename,
                    title: file.title,
                    status: 'success',
                    fileSize: result.fileSize,
                    imageCount: result.imageCount,
                    outputPath: outputPath
                });

                successCount++;
            } catch (error) {
                console.error(`  ✗ 失败: ${error.message}`);

                results.push({
                    file: file.filename,
                    title: file.title,
                    status: 'failed',
                    error: error.message
                });

                failCount++;
            }

            // 文件间延迟
            if (j < batch.length - 1) {
                await new Promise(resolve => setTimeout(resolve, CONFIG.delayBetweenFiles));
            }
        }

        // 批次间延迟
        if (i + CONFIG.batchSize < files.length) {
            console.log(`  等待 ${CONFIG.delayBetweenBatches/1000} 秒后处理下一批...`);
            await new Promise(resolve => setTimeout(resolve, CONFIG.delayBetweenBatches));
        }
    }

    // 保存处理结果
    const resultFile = path.join(outputDir, 'conversion_results.json');
    fs.writeFileSync(resultFile, JSON.stringify({
        mainFile: mainFile,
        mainTitle: mainTitle,
        totalFiles: files.length,
        successCount: successCount,
        failCount: failCount,
        results: results,
        timestamp: new Date().toISOString()
    }, null, 2));

    console.log(`\n${mainTitle} 处理完成:`);
    console.log(`  成功: ${successCount}`);
    console.log(`  失败: ${failCount}`);
    console.log(`  结果文件: ${resultFile}`);

    return { success: successCount, failed: failCount, results: results };
}

/**
 * 主函数 - 批量转换所有文档
 */
async function main() {
    console.log('火鸟门户帮助文档 最终版HTML转PDF转换工具');
    console.log('==========================================');
    console.log('✅ 包含完整正文内容');
    console.log('✅ 嵌入所有配图（base64编码）');
    console.log('✅ 保持原有格式和布局');
    console.log('✅ 按文档结构组织输出');
    console.log('==========================================');

    // 创建主输出目录
    if (!fs.existsSync(CONFIG.outputDir)) {
        fs.mkdirSync(CONFIG.outputDir, { recursive: true });
    }

    // 启动浏览器
    console.log('\n启动浏览器...');
    const browser = await chromium.launch({
        headless: true
    });

    let totalSuccess = 0;
    let totalFailed = 0;
    const allResults = [];

    try {
        // 处理每个主文档
        for (const mainFile of CONFIG.mainFiles) {
            const mainFilePath = path.join(CONFIG.baseDir, mainFile);

            if (!fs.existsSync(mainFilePath)) {
                console.error(`主文件不存在: ${mainFilePath}`);
                continue;
            }

            const result = await convertMainDocument(mainFile, browser);
            totalSuccess += result.success;
            totalFailed += result.failed;
            allResults.push({
                mainFile: mainFile,
                mainTitle: getMainFileTitle(mainFile),
                ...result
            });
        }

    } finally {
        await browser.close();
    }

    // 生成总体报告
    const reportFile = path.join(CONFIG.outputDir, 'final_conversion_report.json');
    fs.writeFileSync(reportFile, JSON.stringify({
        totalFiles: totalSuccess + totalFailed,
        totalSuccess: totalSuccess,
        totalFailed: totalFailed,
        successRate: totalSuccess > 0 ? ((totalSuccess / (totalSuccess + totalFailed)) * 100).toFixed(2) + '%' : '0%',
        results: allResults,
        config: CONFIG,
        timestamp: new Date().toISOString()
    }, null, 2));

    console.log('\n==========================================');
    console.log('🎉 最终版批量转换完成！');
    console.log(`总文件数: ${totalSuccess + totalFailed}`);
    console.log(`总成功: ${totalSuccess}`);
    console.log(`总失败: ${totalFailed}`);
    console.log(`成功率: ${totalSuccess > 0 ? ((totalSuccess / (totalSuccess + totalFailed)) * 100).toFixed(2) + '%' : '0%'}`);
    console.log(`输出目录: ${CONFIG.outputDir}`);
    console.log(`总体报告: ${reportFile}`);
    console.log('==========================================');
}

// 执行主函数
if (require.main === module) {
    main().catch(error => {
        console.error('程序执行失败:', error);
        process.exit(1);
    });
}
