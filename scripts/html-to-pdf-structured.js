const { chromium } = require('playwright');
const fs = require('fs');
const path = require('path');
const { JSDOM } = require('jsdom');

// 配置常量
const CONFIG = {
    baseDir: '/Users/<USER>/Desktop/Trea ai /data/help/help.kumanyun.com',
    outputDir: '/Users/<USER>/Desktop/Trea ai /data/help/pdf-structured',
    mainFiles: [
        'help-4.html',  // 网站后台
        'help-5.html',  // 功能详解
        'help-7.html'   // 二次开发
    ],
    pdfOptions: {
        format: 'A4',
        printBackground: true,
        margin: {
            top: '20px',
            right: '20px',
            bottom: '20px',
            left: '20px'
        }
    }
};

/**
 * 获取图片的MIME类型
 */
function getImageMimeType(filePath) {
    const ext = path.extname(filePath).toLowerCase();
    const mimeTypes = {
        '.png': 'image/png',
        '.jpg': 'image/jpeg',
        '.jpeg': 'image/jpeg',
        '.gif': 'image/gif',
        '.webp': 'image/webp',
        '.svg': 'image/svg+xml'
    };
    return mimeTypes[ext] || 'image/png';
}

/**
 * 将图片转换为base64 data URL
 */
function imageToBase64(imagePath) {
    try {
        const imageBuffer = fs.readFileSync(imagePath);
        const mimeType = getImageMimeType(imagePath);
        const base64 = imageBuffer.toString('base64');
        return `data:${mimeType};base64,${base64}`;
    } catch (error) {
        return null;
    }
}

/**
 * 解析HTML文档的层级结构
 */
function parseHierarchicalStructure(htmlFilePath) {
    try {
        const htmlContent = fs.readFileSync(htmlFilePath, 'utf8');
        const dom = new JSDOM(htmlContent);
        const document = dom.window.document;
        
        const structure = {
            mainTitle: '',
            categories: []
        };
        
        // 获取主标题
        const mainTitleElement = document.querySelector('.content h3.title');
        structure.mainTitle = mainTitleElement ? mainTitleElement.textContent.trim() : 
                              path.basename(htmlFilePath, '.html');
        
        // 解析导航菜单的层级结构
        const menuList = document.querySelector('.menu-list');
        if (!menuList) {
            throw new Error('未找到菜单列表');
        }
        
        let currentCategory = null;
        let currentLevel1 = null;
        let currentLevel2 = null;
        
        // 遍历所有菜单项
        const menuItems = menuList.querySelectorAll('li');
        
        menuItems.forEach(item => {
            const link = item.querySelector('a[href^="help-"]');
            if (!link) return;
            
            const href = link.getAttribute('href');
            const title = link.textContent.trim();
            const classList = Array.from(item.classList);
            
            // 判断层级
            if (classList.includes('level1')) {
                // 第一级目录（一级模块）
                currentCategory = {
                    title: title,
                    href: href,
                    level: 1,
                    subModules: []
                };
                structure.categories.push(currentCategory);
                currentLevel1 = null;
                currentLevel2 = null;
                
            } else if (classList.includes('level2')) {
                // 第二级目录（二级子模块）
                if (currentCategory) {
                    currentLevel1 = {
                        title: title,
                        href: href,
                        level: 2,
                        pages: []
                    };
                    currentCategory.subModules.push(currentLevel1);
                    currentLevel2 = null;
                }
                
            } else if (classList.includes('level3')) {
                // 第三级目录（页面标题）
                if (currentLevel1) {
                    currentLevel2 = {
                        title: title,
                        href: href,
                        level: 3,
                        articles: []
                    };
                    currentLevel1.pages.push(currentLevel2);
                }
                
            } else if (classList.includes('level4') || classList.includes('article')) {
                // 第四级正文（实际内容条目）
                const article = {
                    title: title,
                    href: href,
                    level: 4,
                    type: 'article'
                };
                
                if (currentLevel2) {
                    currentLevel2.articles.push(article);
                } else if (currentLevel1) {
                    if (!currentLevel1.articles) currentLevel1.articles = [];
                    currentLevel1.articles.push(article);
                } else if (currentCategory) {
                    if (!currentCategory.articles) currentCategory.articles = [];
                    currentCategory.articles.push(article);
                }
            }
        });
        
        return structure;
        
    } catch (error) {
        console.error(`解析层级结构失败: ${htmlFilePath}`, error);
        return null;
    }
}

/**
 * 递归收集所有文章
 */
function collectAllArticles(structure) {
    const articles = [];
    
    function traverse(node, path = []) {
        if (node.articles) {
            node.articles.forEach(article => {
                articles.push({
                    ...article,
                    path: [...path, node.title],
                    fullPath: [...path, node.title, article.title].join(' > ')
                });
            });
        }
        
        if (node.subModules) {
            node.subModules.forEach(subModule => {
                traverse(subModule, [...path, node.title]);
            });
        }
        
        if (node.pages) {
            node.pages.forEach(page => {
                traverse(page, [...path, node.title]);
            });
        }
    }
    
    structure.categories.forEach(category => {
        traverse(category);
    });
    
    return articles;
}

/**
 * 按分类收集文章
 */
function collectArticlesByCategory(structure) {
    const collections = {
        systemIntro: [],      // 系统入门
        backendBasic: [],     // 网站后台（排除模块）
        modules: {},          // 功能模块（按模块分组）
        features: [],         // 功能详解
        development: []       // 二次开发
    };
    
    structure.categories.forEach(category => {
        const categoryTitle = category.title;
        
        if (categoryTitle === '系统入门') {
            collections.systemIntro = collectCategoryArticles(category);
        } else if (categoryTitle === '网站后台') {
            // 收集网站后台，但排除"模块"子模块
            category.subModules.forEach(subModule => {
                if (subModule.title === '模块') {
                    // 收集模块下的每个功能模块
                    subModule.pages.forEach(page => {
                        const moduleArticles = collectPageArticles(page);
                        if (moduleArticles.length > 0) {
                            collections.modules[page.title] = moduleArticles;
                        }
                    });
                } else {
                    // 收集其他子模块到网站后台基础内容
                    collections.backendBasic = collections.backendBasic.concat(collectSubModuleArticles(subModule));
                }
            });
        } else if (categoryTitle === '功能详解') {
            collections.features = collectCategoryArticles(category);
        } else if (categoryTitle === '二次开发') {
            collections.development = collectCategoryArticles(category);
        }
    });
    
    return collections;
}

/**
 * 收集分类下的所有文章
 */
function collectCategoryArticles(category) {
    const articles = [];
    
    function traverse(node, path = []) {
        if (node.articles) {
            node.articles.forEach(article => {
                articles.push({
                    ...article,
                    path: [...path, node.title],
                    fullPath: [...path, node.title, article.title].join(' > ')
                });
            });
        }
        
        if (node.subModules) {
            node.subModules.forEach(subModule => {
                traverse(subModule, [...path, node.title]);
            });
        }
        
        if (node.pages) {
            node.pages.forEach(page => {
                traverse(page, [...path, node.title]);
            });
        }
    }
    
    traverse(category);
    return articles;
}

/**
 * 收集子模块下的所有文章
 */
function collectSubModuleArticles(subModule) {
    const articles = [];
    
    function traverse(node, path = []) {
        if (node.articles) {
            node.articles.forEach(article => {
                articles.push({
                    ...article,
                    path: [...path, node.title],
                    fullPath: [...path, node.title, article.title].join(' > ')
                });
            });
        }
        
        if (node.pages) {
            node.pages.forEach(page => {
                traverse(page, [...path, node.title]);
            });
        }
    }
    
    traverse(subModule);
    return articles;
}

/**
 * 收集页面下的所有文章
 */
function collectPageArticles(page) {
    const articles = [];
    
    if (page.articles) {
        page.articles.forEach(article => {
            articles.push({
                ...article,
                path: [page.title],
                fullPath: [page.title, article.title].join(' > ')
            });
        });
    }
    
    return articles;
}

/**
 * 提取单个HTML文件的内容
 */
function extractArticleContent(htmlFilePath) {
    try {
        const htmlContent = fs.readFileSync(htmlFilePath, 'utf8');
        const dom = new JSDOM(htmlContent);
        const document = dom.window.document;

        // 提取页面标题
        const titleElement = document.querySelector('.content h3.title');
        const pageTitle = titleElement ? titleElement.textContent.trim() :
                         path.basename(htmlFilePath, '.html');

        // 提取面包屑导航
        const breadElement = document.querySelector('.bread');
        const breadcrumb = breadElement ? breadElement.textContent.trim() : '';

        // 提取主要内容区域
        const contentElement = document.querySelector('.content');
        if (!contentElement) {
            throw new Error('未找到内容区域');
        }

        // 处理图片：转换为base64
        const images = contentElement.querySelectorAll('img');
        let validImageCount = 0;

        images.forEach((img, index) => {
            const src = img.getAttribute('src');
            if (src && src.startsWith('upload/')) {
                const imagePath = path.join(CONFIG.baseDir, src);

                if (fs.existsSync(imagePath)) {
                    const base64Url = imageToBase64(imagePath);
                    if (base64Url) {
                        img.setAttribute('src', base64Url);
                        if (!img.getAttribute('alt')) {
                            img.setAttribute('alt', `图片 ${index + 1}`);
                        }
                        validImageCount++;
                    } else {
                        img.remove();
                    }
                } else {
                    img.remove();
                }
            }
        });

        return {
            title: pageTitle,
            breadcrumb: breadcrumb,
            content: contentElement.innerHTML,
            imageCount: validImageCount,
            hasImages: validImageCount > 0
        };

    } catch (error) {
        console.error(`提取内容失败: ${htmlFilePath}`, error);
        return null;
    }
}

/**
 * 合并多个文章为一个HTML文档
 */
function mergeArticlesToHTML(articles, documentTitle, documentDescription = '') {
    const mergedContents = [];
    let totalImages = 0;

    // 生成目录
    const tocItems = articles.map((article, index) => {
        return `<li><a href="#article-${index + 1}">${article.title}</a></li>`;
    }).join('');

    const toc = `
    <div class="table-of-contents">
        <h2>目录</h2>
        <ol>
            ${tocItems}
        </ol>
    </div>
    <div style="page-break-after: always;"></div>
    `;

    // 处理每个文章
    articles.forEach((article, index) => {
        const articlePath = path.join(CONFIG.baseDir, article.href);

        if (fs.existsSync(articlePath)) {
            const content = extractArticleContent(articlePath);
            if (content) {
                totalImages += content.imageCount;

                const articleHtml = `
                <div class="article-section" id="article-${index + 1}">
                    <div class="article-header">
                        <h2 class="article-title">${content.title}</h2>
                        ${content.breadcrumb ? `<div class="article-breadcrumb">${content.breadcrumb}</div>` : ''}
                    </div>
                    <div class="article-content">
                        ${content.content}
                    </div>
                    ${index < articles.length - 1 ? '<div style="page-break-after: always;"></div>' : ''}
                </div>
                `;

                mergedContents.push(articleHtml);
            }
        }
    });

    // 创建完整的HTML文档
    const fullHtml = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${documentTitle}</title>
    <style>
        body {
            font-family: "Microsoft YaHei", "PingFang SC", "Helvetica Neue", Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #fff;
        }

        .document-header {
            text-align: center;
            margin-bottom: 40px;
            padding-bottom: 20px;
            border-bottom: 3px solid #007bff;
        }

        .document-title {
            font-size: 32px;
            font-weight: bold;
            color: #007bff;
            margin-bottom: 10px;
        }

        .document-description {
            font-size: 16px;
            color: #666;
            margin-bottom: 20px;
        }

        .document-info {
            font-size: 14px;
            color: #999;
        }

        .table-of-contents {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 8px;
            margin-bottom: 30px;
        }

        .table-of-contents h2 {
            color: #007bff;
            margin-bottom: 15px;
            font-size: 24px;
        }

        .table-of-contents ol {
            padding-left: 20px;
        }

        .table-of-contents li {
            margin-bottom: 8px;
            line-height: 1.5;
        }

        .table-of-contents a {
            color: #333;
            text-decoration: none;
            font-size: 15px;
        }

        .table-of-contents a:hover {
            color: #007bff;
            text-decoration: underline;
        }

        .article-section {
            margin-bottom: 40px;
        }

        .article-header {
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #eee;
        }

        .article-title {
            font-size: 24px;
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
        }

        .article-breadcrumb {
            font-size: 14px;
            color: #666;
            background: #f8f9fa;
            padding: 8px 12px;
            border-radius: 4px;
        }

        .article-content {
            font-size: 15px;
            line-height: 1.8;
        }

        .article-content img {
            max-width: 100% !important;
            height: auto !important;
            display: block;
            margin: 15px auto;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            page-break-inside: avoid;
        }

        .article-content p {
            margin: 10px 0;
            page-break-inside: avoid;
        }

        .article-content strong {
            color: #007bff;
            font-weight: bold;
        }

        .article-content a {
            color: #007bff;
            text-decoration: none;
        }

        .lake-content {
            margin: 15px 0;
        }

        .ne-p {
            margin: 8px 0;
            line-height: 1.6;
        }

        @media print {
            body { margin: 0; }
            .article-content img {
                max-width: 100% !important;
                page-break-inside: avoid;
            }
        }
    </style>
</head>
<body>
    <div class="document-header">
        <h1 class="document-title">${documentTitle}</h1>
        ${documentDescription ? `<div class="document-description">${documentDescription}</div>` : ''}
        <div class="document-info">
            包含 ${articles.length} 个章节 | ${totalImages} 张配图 | 生成时间：${new Date().toLocaleString('zh-CN')}
        </div>
    </div>

    ${toc}

    ${mergedContents.join('')}
</body>
</html>`;

    return {
        html: fullHtml,
        articleCount: articles.length,
        imageCount: totalImages
    };
}

/**
 * 生成PDF
 */
async function generatePDF(htmlContent, outputPath, browser) {
    try {
        const page = await browser.newPage();
        await page.setViewportSize({ width: 1200, height: 800 });

        await page.setContent(htmlContent, {
            waitUntil: 'networkidle',
            timeout: 60000
        });

        await page.waitForTimeout(2000);

        await page.pdf({
            path: outputPath,
            ...CONFIG.pdfOptions
        });

        await page.close();

        const stats = fs.statSync(outputPath);
        const fileSizeMB = (stats.size / 1024 / 1024).toFixed(2);

        return {
            success: true,
            fileSize: fileSizeMB
        };

    } catch (error) {
        throw error;
    }
}

/**
 * 获取安全的文件名
 */
function getSafeFileName(title) {
    return title.replace(/[\/\\:*?"<>|]/g, '_').substring(0, 100);
}

/**
 * 主要的结构化转换函数
 */
async function generateStructuredPDFs() {
    console.log('火鸟门户系统文档 结构化PDF生成工具');
    console.log('=====================================');
    console.log('📚 按照四级层级结构生成PDF文档集合');
    console.log('✅ 系统入门.pdf');
    console.log('✅ 网站后台.pdf（排除模块）');
    console.log('✅ 31个功能模块独立PDF');
    console.log('✅ 功能详解.pdf');
    console.log('✅ 二次开发.pdf');
    console.log('=====================================');

    // 创建输出目录
    if (!fs.existsSync(CONFIG.outputDir)) {
        fs.mkdirSync(CONFIG.outputDir, { recursive: true });
    }

    // 创建功能模块子目录
    const modulesDir = path.join(CONFIG.outputDir, '03_功能模块');
    if (!fs.existsSync(modulesDir)) {
        fs.mkdirSync(modulesDir, { recursive: true });
    }

    // 启动浏览器
    console.log('\n启动浏览器...');
    const browser = await chromium.launch({ headless: true });

    const results = {
        systemIntro: null,
        backendBasic: null,
        modules: {},
        features: null,
        development: null,
        summary: {
            totalPDFs: 0,
            totalArticles: 0,
            totalImages: 0,
            totalSize: 0
        }
    };

    try {
        // 解析文档结构（使用help-4.html作为主要结构源）
        const mainFilePath = path.join(CONFIG.baseDir, 'help-4.html');
        const structure = parseHierarchicalStructure(mainFilePath);

        if (!structure) {
            throw new Error('无法解析文档结构');
        }

        console.log(`\n解析完成: ${structure.categories.length} 个一级模块`);

        // 按分类收集文章
        const collections = collectArticlesByCategory(structure);

        console.log('\n📊 内容统计:');
        console.log(`系统入门: ${collections.systemIntro.length} 篇文章`);
        console.log(`网站后台基础: ${collections.backendBasic.length} 篇文章`);
        console.log(`功能模块: ${Object.keys(collections.modules).length} 个模块`);
        console.log(`功能详解: ${collections.features.length} 篇文章`);
        console.log(`二次开发: ${collections.development.length} 篇文章`);

        // 1. 生成系统入门.pdf
        if (collections.systemIntro.length > 0) {
            console.log('\n🔄 生成系统入门.pdf...');
            const mergedHtml = mergeArticlesToHTML(
                collections.systemIntro,
                '火鸟门户系统 - 系统入门',
                '包含服务器相关问题、网站系统部署、公司信息、多语言使用教程等完整内容'
            );

            const outputPath = path.join(CONFIG.outputDir, '01_系统入门.pdf');
            const result = await generatePDF(mergedHtml.html, outputPath, browser);

            results.systemIntro = {
                ...result,
                articleCount: mergedHtml.articleCount,
                imageCount: mergedHtml.imageCount,
                outputPath: outputPath
            };

            results.summary.totalPDFs++;
            results.summary.totalArticles += mergedHtml.articleCount;
            results.summary.totalImages += mergedHtml.imageCount;
            results.summary.totalSize += parseFloat(result.fileSize);

            console.log(`✅ 系统入门.pdf 生成完成 (${result.fileSize} MB, ${mergedHtml.articleCount} 篇文章, ${mergedHtml.imageCount} 张图片)`);
        }

        // 2. 生成网站后台.pdf（排除模块）
        if (collections.backendBasic.length > 0) {
            console.log('\n🔄 生成网站后台.pdf...');
            const mergedHtml = mergeArticlesToHTML(
                collections.backendBasic,
                '火鸟门户系统 - 网站后台',
                '包含系统、用户、商家、财务中心、微信、APP、插件管理、商店等基础功能（不包含具体功能模块）'
            );

            const outputPath = path.join(CONFIG.outputDir, '02_网站后台.pdf');
            const result = await generatePDF(mergedHtml.html, outputPath, browser);

            results.backendBasic = {
                ...result,
                articleCount: mergedHtml.articleCount,
                imageCount: mergedHtml.imageCount,
                outputPath: outputPath
            };

            results.summary.totalPDFs++;
            results.summary.totalArticles += mergedHtml.articleCount;
            results.summary.totalImages += mergedHtml.imageCount;
            results.summary.totalSize += parseFloat(result.fileSize);

            console.log(`✅ 网站后台.pdf 生成完成 (${result.fileSize} MB, ${mergedHtml.articleCount} 篇文章, ${mergedHtml.imageCount} 张图片)`);
        }

        // 3. 生成功能模块独立PDF
        console.log('\n🔄 生成功能模块独立PDF...');
        const moduleNames = Object.keys(collections.modules);
        console.log(`共 ${moduleNames.length} 个功能模块`);

        for (let i = 0; i < moduleNames.length; i++) {
            const moduleName = moduleNames[i];
            const moduleArticles = collections.modules[moduleName];

            if (moduleArticles.length > 0) {
                console.log(`  [${i + 1}/${moduleNames.length}] ${moduleName}...`);

                const mergedHtml = mergeArticlesToHTML(
                    moduleArticles,
                    `火鸟门户系统 - ${moduleName}`,
                    `${moduleName}功能模块的完整使用教程和配置说明`
                );

                const safeFileName = getSafeFileName(moduleName);
                const outputPath = path.join(modulesDir, `${String(i + 1).padStart(2, '0')}_${safeFileName}.pdf`);
                const result = await generatePDF(mergedHtml.html, outputPath, browser);

                results.modules[moduleName] = {
                    ...result,
                    articleCount: mergedHtml.articleCount,
                    imageCount: mergedHtml.imageCount,
                    outputPath: outputPath
                };

                results.summary.totalPDFs++;
                results.summary.totalArticles += mergedHtml.articleCount;
                results.summary.totalImages += mergedHtml.imageCount;
                results.summary.totalSize += parseFloat(result.fileSize);

                console.log(`    ✅ ${moduleName}.pdf (${result.fileSize} MB, ${mergedHtml.articleCount} 篇文章, ${mergedHtml.imageCount} 张图片)`);

                // 添加延迟避免过载
                if (i < moduleNames.length - 1) {
                    await new Promise(resolve => setTimeout(resolve, 1000));
                }
            }
        }

        // 4. 生成功能详解.pdf
        if (collections.features.length > 0) {
            console.log('\n🔄 生成功能详解.pdf...');
            const mergedHtml = mergeArticlesToHTML(
                collections.features,
                '火鸟门户系统 - 功能详解',
                '包含第三方配置、第三方小程序、APP相关配置、可选功能配置、常见使用问题、新客户引导手册等完整内容'
            );

            const outputPath = path.join(CONFIG.outputDir, '04_功能详解.pdf');
            const result = await generatePDF(mergedHtml.html, outputPath, browser);

            results.features = {
                ...result,
                articleCount: mergedHtml.articleCount,
                imageCount: mergedHtml.imageCount,
                outputPath: outputPath
            };

            results.summary.totalPDFs++;
            results.summary.totalArticles += mergedHtml.articleCount;
            results.summary.totalImages += mergedHtml.imageCount;
            results.summary.totalSize += parseFloat(result.fileSize);

            console.log(`✅ 功能详解.pdf 生成完成 (${result.fileSize} MB, ${mergedHtml.articleCount} 篇文章, ${mergedHtml.imageCount} 张图片)`);
        }

        // 5. 生成二次开发.pdf
        if (collections.development.length > 0) {
            console.log('\n🔄 生成二次开发.pdf...');
            const mergedHtml = mergeArticlesToHTML(
                collections.development,
                '火鸟门户系统 - 二次开发',
                '包含模板制作、程序开发等完整的二次开发文档'
            );

            const outputPath = path.join(CONFIG.outputDir, '05_二次开发.pdf');
            const result = await generatePDF(mergedHtml.html, outputPath, browser);

            results.development = {
                ...result,
                articleCount: mergedHtml.articleCount,
                imageCount: mergedHtml.imageCount,
                outputPath: outputPath
            };

            results.summary.totalPDFs++;
            results.summary.totalArticles += mergedHtml.articleCount;
            results.summary.totalImages += mergedHtml.imageCount;
            results.summary.totalSize += parseFloat(result.fileSize);

            console.log(`✅ 二次开发.pdf 生成完成 (${result.fileSize} MB, ${mergedHtml.articleCount} 篇文章, ${mergedHtml.imageCount} 张图片)`);
        }

    } catch (error) {
        console.error('生成过程中出错:', error);
    } finally {
        await browser.close();
    }

    return results;
}

/**
 * 生成详细报告
 */
function generateDetailedReport(results) {
    const report = {
        timestamp: new Date().toISOString(),
        summary: {
            ...results.summary,
            totalSize: `${results.summary.totalSize.toFixed(2)} MB`
        },
        documents: []
    };

    // 添加各个文档的详细信息
    if (results.systemIntro) {
        report.documents.push({
            name: '系统入门',
            filename: '01_系统入门.pdf',
            articleCount: results.systemIntro.articleCount,
            imageCount: results.systemIntro.imageCount,
            fileSize: results.systemIntro.fileSize + ' MB',
            description: '包含服务器相关问题、网站系统部署、公司信息、多语言使用教程等完整内容'
        });
    }

    if (results.backendBasic) {
        report.documents.push({
            name: '网站后台',
            filename: '02_网站后台.pdf',
            articleCount: results.backendBasic.articleCount,
            imageCount: results.backendBasic.imageCount,
            fileSize: results.backendBasic.fileSize + ' MB',
            description: '包含系统、用户、商家、财务中心、微信、APP、插件管理、商店等基础功能'
        });
    }

    // 添加功能模块
    const moduleNames = Object.keys(results.modules);
    moduleNames.forEach((moduleName, index) => {
        const moduleResult = results.modules[moduleName];
        report.documents.push({
            name: `功能模块 - ${moduleName}`,
            filename: `03_功能模块/${String(index + 1).padStart(2, '0')}_${getSafeFileName(moduleName)}.pdf`,
            articleCount: moduleResult.articleCount,
            imageCount: moduleResult.imageCount,
            fileSize: moduleResult.fileSize + ' MB',
            description: `${moduleName}功能模块的完整使用教程和配置说明`
        });
    });

    if (results.features) {
        report.documents.push({
            name: '功能详解',
            filename: '04_功能详解.pdf',
            articleCount: results.features.articleCount,
            imageCount: results.features.imageCount,
            fileSize: results.features.fileSize + ' MB',
            description: '包含第三方配置、第三方小程序、APP相关配置、可选功能配置、常见使用问题等'
        });
    }

    if (results.development) {
        report.documents.push({
            name: '二次开发',
            filename: '05_二次开发.pdf',
            articleCount: results.development.articleCount,
            imageCount: results.development.imageCount,
            fileSize: results.development.fileSize + ' MB',
            description: '包含模板制作、程序开发等完整的二次开发文档'
        });
    }

    return report;
}

/**
 * 主函数
 */
async function main() {
    try {
        const results = await generateStructuredPDFs();

        // 生成详细报告
        const report = generateDetailedReport(results);
        const reportFile = path.join(CONFIG.outputDir, 'conversion_report.json');
        fs.writeFileSync(reportFile, JSON.stringify(report, null, 2));

        // 显示最终结果
        console.log('\n=====================================');
        console.log('🎉 火鸟门户系统文档 结构化PDF生成完成！');
        console.log('=====================================');
        console.log(`📊 总体统计:`);
        console.log(`  PDF文件数: ${results.summary.totalPDFs}`);
        console.log(`  文章总数: ${results.summary.totalArticles}`);
        console.log(`  图片总数: ${results.summary.totalImages}`);
        console.log(`  总文件大小: ${results.summary.totalSize.toFixed(2)} MB`);
        console.log(`\n📁 输出目录: ${CONFIG.outputDir}`);
        console.log(`📋 详细报告: ${reportFile}`);

        console.log('\n📚 生成的PDF文档:');
        report.documents.forEach((doc, index) => {
            console.log(`  ${index + 1}. ${doc.name}`);
            console.log(`     文件: ${doc.filename}`);
            console.log(`     内容: ${doc.articleCount} 篇文章, ${doc.imageCount} 张图片, ${doc.fileSize}`);
        });

        console.log('\n✅ 所有PDF文档已按照火鸟门户系统的四级层级结构生成完成！');
        console.log('✅ 每个PDF都包含完整的正文内容和配图（base64编码嵌入）');
        console.log('✅ 保持了原有的文档格式和布局');
        console.log('✅ 按照层级结构组织了文件夹和文件命名');

    } catch (error) {
        console.error('程序执行失败:', error);
        process.exit(1);
    }
}

// 执行主函数
if (require.main === module) {
    main();
}
