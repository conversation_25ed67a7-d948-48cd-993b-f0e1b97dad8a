const { chromium } = require('playwright');
const fs = require('fs');
const path = require('path');
const { JSDOM } = require('jsdom');

// 配置常量
const CONFIG = {
    baseDir: '/Users/<USER>/Desktop/Trea ai /data/help/help.kumanyun.com',
    outputDir: '/Users/<USER>/Desktop/Trea ai /data/help/pdf-hierarchical',
    mainFiles: [
        'help-4.html',  // 网站后台
        'help-5.html',  // 功能详解
        'help-7.html'   // 二次开发
    ],
    pdfOptions: {
        format: 'A4',
        printBackground: true,
        margin: {
            top: '20px',
            right: '20px',
            bottom: '20px',
            left: '20px'
        }
    }
};

/**
 * 获取图片的MIME类型
 */
function getImageMimeType(filePath) {
    const ext = path.extname(filePath).toLowerCase();
    const mimeTypes = {
        '.png': 'image/png',
        '.jpg': 'image/jpeg',
        '.jpeg': 'image/jpeg',
        '.gif': 'image/gif',
        '.webp': 'image/webp',
        '.svg': 'image/svg+xml'
    };
    return mimeTypes[ext] || 'image/png';
}

/**
 * 将图片转换为base64 data URL
 */
function imageToBase64(imagePath) {
    try {
        const imageBuffer = fs.readFileSync(imagePath);
        const mimeType = getImageMimeType(imagePath);
        const base64 = imageBuffer.toString('base64');
        return `data:${mimeType};base64,${base64}`;
    } catch (error) {
        return null;
    }
}

/**
 * 解析HTML文档的层级结构
 * 根据火鸟文档结构图片的四级层级来解析
 */
function parseHierarchicalStructure(htmlFilePath) {
    try {
        const htmlContent = fs.readFileSync(htmlFilePath, 'utf8');
        const dom = new JSDOM(htmlContent);
        const document = dom.window.document;
        
        const structure = {
            mainTitle: '',
            categories: [] // 总分类模块
        };
        
        // 获取主标题
        const mainTitleElement = document.querySelector('.content h3.title');
        structure.mainTitle = mainTitleElement ? mainTitleElement.textContent.trim() : 
                              path.basename(htmlFilePath, '.html');
        
        // 解析导航菜单的层级结构
        const menuList = document.querySelector('.menu-list');
        if (!menuList) {
            throw new Error('未找到菜单列表');
        }
        
        let currentCategory = null;
        let currentLevel1 = null;
        let currentLevel2 = null;
        
        // 遍历所有菜单项
        const menuItems = menuList.querySelectorAll('li');
        
        menuItems.forEach(item => {
            const link = item.querySelector('a[href^="help-"]');
            if (!link) return;
            
            const href = link.getAttribute('href');
            const title = link.textContent.trim();
            const classList = Array.from(item.classList);
            
            // 判断层级
            if (classList.includes('level1')) {
                // 第一级目录（一级模块）
                currentCategory = {
                    title: title,
                    href: href,
                    level: 1,
                    subModules: [] // 二级子模块
                };
                structure.categories.push(currentCategory);
                currentLevel1 = null;
                currentLevel2 = null;
                
            } else if (classList.includes('level2')) {
                // 第二级目录（二级子模块）
                if (currentCategory) {
                    currentLevel1 = {
                        title: title,
                        href: href,
                        level: 2,
                        pages: [] // 页面标题
                    };
                    currentCategory.subModules.push(currentLevel1);
                    currentLevel2 = null;
                }
                
            } else if (classList.includes('level3')) {
                // 第三级目录（页面标题）
                if (currentLevel1) {
                    currentLevel2 = {
                        title: title,
                        href: href,
                        level: 3,
                        articles: [] // 实际内容条目
                    };
                    currentLevel1.pages.push(currentLevel2);
                }
                
            } else if (classList.includes('level4') || classList.includes('article')) {
                // 第四级正文（实际内容条目）
                const article = {
                    title: title,
                    href: href,
                    level: 4,
                    type: 'article'
                };
                
                if (currentLevel2) {
                    currentLevel2.articles.push(article);
                } else if (currentLevel1) {
                    // 如果没有level3，直接添加到level2
                    if (!currentLevel1.articles) currentLevel1.articles = [];
                    currentLevel1.articles.push(article);
                } else if (currentCategory) {
                    // 如果没有level2，直接添加到level1
                    if (!currentCategory.articles) currentCategory.articles = [];
                    currentCategory.articles.push(article);
                }
            }
        });
        
        return structure;
        
    } catch (error) {
        console.error(`解析层级结构失败: ${htmlFilePath}`, error);
        return null;
    }
}

/**
 * 提取并修复HTML内容，将图片转换为base64
 */
function extractAndFixContent(htmlFilePath) {
    try {
        const htmlContent = fs.readFileSync(htmlFilePath, 'utf8');
        const dom = new JSDOM(htmlContent);
        const document = dom.window.document;
        
        // 提取页面标题
        const titleElement = document.querySelector('.content h3.title');
        const pageTitle = titleElement ? titleElement.textContent.trim() : 
                         path.basename(htmlFilePath, '.html');
        
        // 提取面包屑导航
        const breadElement = document.querySelector('.bread');
        const breadcrumb = breadElement ? breadElement.textContent.trim() : '';
        
        // 提取主要内容区域
        const contentElement = document.querySelector('.content');
        if (!contentElement) {
            throw new Error('未找到内容区域');
        }
        
        // 处理图片：转换为base64
        const images = contentElement.querySelectorAll('img');
        let validImageCount = 0;
        
        images.forEach((img, index) => {
            const src = img.getAttribute('src');
            if (src && src.startsWith('upload/')) {
                const imagePath = path.join(CONFIG.baseDir, src);
                
                if (fs.existsSync(imagePath)) {
                    const base64Url = imageToBase64(imagePath);
                    if (base64Url) {
                        img.setAttribute('src', base64Url);
                        if (!img.getAttribute('alt')) {
                            img.setAttribute('alt', `图片 ${index + 1}`);
                        }
                        validImageCount++;
                    } else {
                        img.remove();
                    }
                } else {
                    img.remove();
                }
            }
        });
        
        // 创建完整的HTML文档
        const fullHtml = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${pageTitle}</title>
    <style>
        body {
            font-family: "Microsoft YaHei", "PingFang SC", "Helvetica Neue", Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #fff;
        }
        .breadcrumb {
            font-size: 14px;
            color: #666;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }
        .title {
            font-size: 24px;
            font-weight: bold;
            color: #333;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #007bff;
        }
        .content {
            font-size: 15px;
            line-height: 1.8;
        }
        .content img {
            max-width: 100% !important;
            height: auto !important;
            display: block;
            margin: 15px auto;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            page-break-inside: avoid;
        }
        .content p {
            margin: 10px 0;
            page-break-inside: avoid;
        }
        .content strong {
            color: #007bff;
            font-weight: bold;
        }
        .content a {
            color: #007bff;
            text-decoration: none;
        }
        .lake-content {
            margin: 15px 0;
        }
        .ne-p {
            margin: 8px 0;
            line-height: 1.6;
        }
        @media print {
            body { margin: 0; }
            .content img { 
                max-width: 100% !important;
                page-break-inside: avoid;
            }
        }
    </style>
</head>
<body>
    ${breadcrumb ? `<div class="breadcrumb">${breadcrumb}</div>` : ''}
    <h1 class="title">${pageTitle}</h1>
    <div class="content">
        ${contentElement.innerHTML}
    </div>
</body>
</html>`;
        
        return {
            title: pageTitle,
            breadcrumb: breadcrumb,
            content: fullHtml,
            imageCount: validImageCount,
            hasImages: validImageCount > 0
        };
        
    } catch (error) {
        console.error(`提取内容失败: ${htmlFilePath}`, error);
        return null;
    }
}

/**
 * 转换HTML为PDF
 */
async function convertToPdf(htmlFilePath, outputPath, browser) {
    try {
        const page = await browser.newPage();
        await page.setViewportSize({ width: 1200, height: 800 });

        const contentData = extractAndFixContent(htmlFilePath);
        if (!contentData) {
            throw new Error('无法提取页面内容');
        }

        await page.setContent(contentData.content, {
            waitUntil: 'networkidle',
            timeout: 30000
        });

        await page.waitForTimeout(1000);

        await page.pdf({
            path: outputPath,
            ...CONFIG.pdfOptions
        });

        await page.close();

        const stats = fs.statSync(outputPath);
        const fileSizeMB = (stats.size / 1024 / 1024).toFixed(2);

        return {
            success: true,
            fileSize: fileSizeMB,
            title: contentData.title,
            imageCount: contentData.imageCount
        };

    } catch (error) {
        throw error;
    }
}

/**
 * 获取安全的文件名
 */
function getSafeFileName(title) {
    return title.replace(/[\/\\:*?"<>|]/g, '_').substring(0, 100);
}

/**
 * 递归收集所有文章
 */
function collectAllArticles(structure) {
    const articles = [];

    function traverse(node, path = []) {
        if (node.articles) {
            node.articles.forEach(article => {
                articles.push({
                    ...article,
                    path: [...path, node.title],
                    fullPath: [...path, node.title, article.title].join(' > ')
                });
            });
        }

        if (node.subModules) {
            node.subModules.forEach(subModule => {
                traverse(subModule, [...path, node.title]);
            });
        }

        if (node.pages) {
            node.pages.forEach(page => {
                traverse(page, [...path, node.title]);
            });
        }
    }

    structure.categories.forEach(category => {
        traverse(category);
    });

    return articles;
}

/**
 * 测试层级结构解析
 */
async function testHierarchicalParsing() {
    console.log('火鸟门户文档层级结构解析测试');
    console.log('================================');

    // 创建输出目录
    if (!fs.existsSync(CONFIG.outputDir)) {
        fs.mkdirSync(CONFIG.outputDir, { recursive: true });
    }

    // 测试解析每个主文件的结构
    for (const mainFile of CONFIG.mainFiles) {
        const mainFilePath = path.join(CONFIG.baseDir, mainFile);

        if (!fs.existsSync(mainFilePath)) {
            console.log(`文件不存在: ${mainFile}`);
            continue;
        }

        console.log(`\n解析文档: ${mainFile}`);
        console.log('-'.repeat(30));

        const structure = parseHierarchicalStructure(mainFilePath);
        if (!structure) {
            console.log('解析失败');
            continue;
        }

        console.log(`主标题: ${structure.mainTitle}`);
        console.log(`总分类数: ${structure.categories.length}`);

        // 显示层级结构
        structure.categories.forEach((category, catIndex) => {
            console.log(`\n${catIndex + 1}. 【一级模块】${category.title}`);

            if (category.subModules && category.subModules.length > 0) {
                category.subModules.forEach((subModule, subIndex) => {
                    console.log(`  ${catIndex + 1}.${subIndex + 1} 【二级子模块】${subModule.title}`);

                    if (subModule.pages && subModule.pages.length > 0) {
                        subModule.pages.forEach((page, pageIndex) => {
                            console.log(`    ${catIndex + 1}.${subIndex + 1}.${pageIndex + 1} 【页面标题】${page.title}`);

                            if (page.articles && page.articles.length > 0) {
                                page.articles.slice(0, 3).forEach((article, artIndex) => {
                                    console.log(`      ${catIndex + 1}.${subIndex + 1}.${pageIndex + 1}.${artIndex + 1} 【内容条目】${article.title}`);
                                });
                                if (page.articles.length > 3) {
                                    console.log(`      ... 还有 ${page.articles.length - 3} 个内容条目`);
                                }
                            }
                        });
                    }

                    if (subModule.articles && subModule.articles.length > 0) {
                        subModule.articles.slice(0, 3).forEach((article, artIndex) => {
                            console.log(`    ${catIndex + 1}.${subIndex + 1}.${artIndex + 1} 【内容条目】${article.title}`);
                        });
                        if (subModule.articles.length > 3) {
                            console.log(`    ... 还有 ${subModule.articles.length - 3} 个内容条目`);
                        }
                    }
                });
            }

            if (category.articles && category.articles.length > 0) {
                category.articles.slice(0, 3).forEach((article, artIndex) => {
                    console.log(`  ${catIndex + 1}.${artIndex + 1} 【内容条目】${article.title}`);
                });
                if (category.articles.length > 3) {
                    console.log(`  ... 还有 ${category.articles.length - 3} 个内容条目`);
                }
            }
        });

        // 统计总文章数
        const allArticles = collectAllArticles(structure);
        console.log(`\n总文章数: ${allArticles.length}`);

        // 保存结构到文件
        const structureFile = path.join(CONFIG.outputDir, `structure_${mainFile.replace('.html', '.json')}`);
        fs.writeFileSync(structureFile, JSON.stringify(structure, null, 2));
        console.log(`结构已保存: ${structureFile}`);
    }

    console.log('\n================================');
    console.log('层级结构解析测试完成！');
}

// 执行测试
if (require.main === module) {
    testHierarchicalParsing().catch(error => {
        console.error('测试失败:', error);
        process.exit(1);
    });
}
