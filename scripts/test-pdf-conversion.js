const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

// 配置常量
const CONFIG = {
    baseDir: '/Users/<USER>/Desktop/Trea ai /data/help/help.kumanyun.com',
    outputDir: '/Users/<USER>/Desktop/Trea ai /data/help/pdf',
    testFiles: [
        'help-4.html',  // 网站后台主页
        'help-246-800.html'  // 一个具体的文章页面
    ],
    pdfOptions: {
        format: 'A4',
        printBackground: true,
        margin: {
            top: '20px',
            right: '20px',
            bottom: '20px',
            left: '20px'
        }
    }
};

/**
 * 转换单个HTML文件为PDF
 * @param {string} htmlFilePath - HTML文件路径
 * @param {string} outputPath - 输出PDF路径
 * @param {Object} browser - Puppeteer浏览器实例
 */
async function convertHtmlToPdf(htmlFilePath, outputPath, browser) {
    try {
        console.log(`开始转换: ${path.basename(htmlFilePath)}`);
        
        const page = await browser.newPage();
        
        // 设置视口大小
        await page.setViewport({ width: 1200, height: 800 });
        
        // 读取HTML文件内容
        const htmlContent = fs.readFileSync(htmlFilePath, 'utf8');
        
        // 修复相对路径，使图片和CSS能正确加载
        const baseUrl = `file://${CONFIG.baseDir}/`;
        const fixedContent = htmlContent.replace(
            /(src|href)="(?!http|\/\/|data:)([^"]+)"/g,
            `$1="${baseUrl}$2"`
        );
        
        console.log(`设置页面内容...`);
        
        // 设置页面内容
        await page.setContent(fixedContent, {
            waitUntil: 'networkidle0',
            timeout: 30000
        });
        
        console.log(`等待图片加载...`);
        
        // 等待图片加载
        await page.evaluate(() => {
            return Promise.all(
                Array.from(document.images, img => {
                    if (img.complete) return Promise.resolve();
                    return new Promise((resolve, reject) => {
                        img.addEventListener('load', resolve);
                        img.addEventListener('error', resolve); // 即使图片加载失败也继续
                        setTimeout(resolve, 5000); // 5秒超时
                    });
                })
            );
        });
        
        console.log(`生成PDF...`);
        
        // 生成PDF
        await page.pdf({
            path: outputPath,
            ...CONFIG.pdfOptions
        });
        
        await page.close();
        console.log(`✓ PDF已生成: ${path.basename(outputPath)}`);
        
        // 检查文件大小
        const stats = fs.statSync(outputPath);
        console.log(`  文件大小: ${(stats.size / 1024 / 1024).toFixed(2)} MB`);
        
    } catch (error) {
        console.error(`✗ 转换失败 ${htmlFilePath}:`, error.message);
        throw error;
    }
}

/**
 * 测试PDF转换功能
 */
async function testPdfConversion() {
    console.log('火鸟门户帮助文档 PDF转换测试');
    console.log('================================');
    
    // 创建输出目录
    if (!fs.existsSync(CONFIG.outputDir)) {
        fs.mkdirSync(CONFIG.outputDir, { recursive: true });
    }
    
    // 启动浏览器
    console.log('启动浏览器...');
    const browser = await puppeteer.launch({
        headless: "new",
        args: [
            '--no-sandbox',
            '--disable-setuid-sandbox',
            '--disable-dev-shm-usage',
            '--disable-gpu',
            '--disable-web-security',
            '--allow-running-insecure-content'
        ]
    });
    
    let successCount = 0;
    let failCount = 0;
    
    try {
        // 测试转换每个文件
        for (const testFile of CONFIG.testFiles) {
            const inputPath = path.join(CONFIG.baseDir, testFile);
            const outputFileName = `test_${testFile.replace('.html', '.pdf')}`;
            const outputPath = path.join(CONFIG.outputDir, outputFileName);
            
            if (!fs.existsSync(inputPath)) {
                console.error(`文件不存在: ${inputPath}`);
                failCount++;
                continue;
            }
            
            try {
                await convertHtmlToPdf(inputPath, outputPath, browser);
                successCount++;
            } catch (error) {
                console.error(`转换失败: ${testFile}`);
                failCount++;
            }
            
            // 添加延迟
            await new Promise(resolve => setTimeout(resolve, 1000));
        }
        
    } finally {
        await browser.close();
    }
    
    console.log('\n================================');
    console.log('测试完成统计:');
    console.log(`成功: ${successCount}`);
    console.log(`失败: ${failCount}`);
    console.log(`输出目录: ${CONFIG.outputDir}`);
}

// 执行测试
if (require.main === module) {
    testPdfConversion().catch(error => {
        console.error('测试失败:', error);
        process.exit(1);
    });
}
