# 火鸟门户帮助文档 HTML转PDF 转换工具

## 概述

这是一个自动化工具，用于将火鸟门户帮助文档的HTML文件批量转换为PDF格式，保持原有的图片、格式和布局。

## 功能特性

- ✅ 自动提取HTML文档的导航结构和所有子页面链接
- ✅ 批量转换HTML文件为高质量PDF
- ✅ 保持原有图片、CSS样式和页面布局
- ✅ 按文档类型和层级组织输出文件
- ✅ 支持断点续传和错误重试
- ✅ 生成详细的转换报告和统计信息

## 文件结构

```
scripts/
├── extract-links.js           # 链接提取和分析工具
├── test-pdf-playwright.js     # PDF转换功能测试
├── html-to-pdf-batch.js       # 主要的批量转换工具
├── package.json               # 依赖管理
└── README.md                  # 本文档

data/help/pdf/                 # PDF输出目录
├── 网站后台/                   # help-4.html 相关文档
│   ├── 001_网站后台.pdf
│   ├── 002_系统入门.pdf
│   ├── 003_服务器相关问题.pdf
│   ├── ...
│   └── conversion_results.json
├── 功能详解/                   # help-5.html 相关文档
│   ├── 001_功能详解.pdf
│   ├── 002_系统入门.pdf
│   ├── ...
│   └── conversion_results.json
├── 二次开发/                   # help-7.html 相关文档
│   ├── 001_二次开发.pdf
│   ├── 002_系统入门.pdf
│   ├── ...
│   └── conversion_results.json
└── batch_conversion_report.json # 总体转换报告
```

## 文件命名规范

### PDF文件命名
- 格式：`{序号}_{标题}.pdf`
- 序号：3位数字，从001开始，按处理顺序递增
- 标题：从HTML文档中提取的页面标题，特殊字符替换为下划线
- 示例：
  - `001_网站后台.pdf`
  - `002_系统入门.pdf`
  - `045_用户管理_添加用户.pdf`

### 目录命名
- 按主文档标题命名：`网站后台`、`功能详解`、`二次开发`
- 避免使用特殊字符和空格

### 报告文件命名
- `conversion_results.json`：单个主文档的转换结果
- `batch_conversion_report.json`：整体批量转换报告
- `links-{主文件名}.json`：链接提取结果

## 安装和使用

### 1. 安装依赖

```bash
cd scripts
npm install jsdom playwright
npx playwright install chromium
```

### 2. 链接分析（可选）

```bash
node extract-links.js
```

这将分析所有主文档的链接结构，生成详细的分析报告。

### 3. 测试转换功能

```bash
node test-pdf-playwright.js
```

测试PDF转换功能，转换少量文件验证效果。

### 4. 批量转换

```bash
node html-to-pdf-batch.js
```

执行完整的批量转换，处理所有877个HTML文件。

## 配置选项

在 `html-to-pdf-batch.js` 中可以调整以下配置：

```javascript
const CONFIG = {
    baseDir: '/Users/<USER>/Desktop/Trea ai /data/help/help.kumanyun.com',
    outputDir: '/Users/<USER>/Desktop/Trea ai /data/help/pdf',
    mainFiles: ['help-4.html', 'help-5.html', 'help-7.html'],
    
    // PDF生成选项
    pdfOptions: {
        format: 'A4',           // 页面格式
        printBackground: true,   // 打印背景
        margin: {               // 页边距
            top: '20px',
            right: '20px',
            bottom: '20px',
            left: '20px'
        }
    },
    
    // 批量处理配置
    batchSize: 5,              // 每批处理文件数
    delayBetweenFiles: 1000,   // 文件间延迟(ms)
    delayBetweenBatches: 3000, // 批次间延迟(ms)
    maxRetries: 2              // 最大重试次数
};
```

## 质量控制

### 自动验证
- 文件存在性检查
- PDF文件大小验证
- 图片加载状态检测
- 错误重试机制

### 手动验证
1. 检查PDF文件是否正常生成
2. 验证图片是否正确显示
3. 确认文本格式和布局
4. 查看转换报告中的错误信息

## 性能优化

- 分批处理避免内存溢出
- 文件间延迟防止系统过载
- 浏览器实例复用提高效率
- 图片加载超时控制

## 故障排除

### 常见问题

1. **浏览器启动失败**
   - 确保已安装 Playwright 浏览器：`npx playwright install chromium`
   - 检查系统权限和安全设置

2. **图片加载失败**
   - 检查图片文件路径是否正确
   - 确认静态资源目录存在

3. **PDF生成失败**
   - 查看错误日志确定具体原因
   - 检查输出目录权限
   - 尝试减少批次大小

4. **内存不足**
   - 减少 `batchSize` 配置
   - 增加 `delayBetweenBatches` 延迟
   - 关闭其他占用内存的程序

### 日志和报告

- 控制台输出：实时显示转换进度和状态
- JSON报告：详细记录每个文件的转换结果
- 错误信息：包含具体的失败原因和建议

## 技术栈

- **Node.js**：运行环境
- **Playwright**：浏览器自动化和PDF生成
- **JSDOM**：HTML解析和DOM操作
- **文件系统**：批量文件处理和目录管理

## 许可证

MIT License
