{"version": 3, "file": "CustomQueryHandler.js", "sourceRoot": "", "sources": ["../../../../src/common/CustomQueryHandler.ts"], "names": [], "mappings": ";AAAA;;;;GAIG;;;AAGH,iDAAyC;AACzC,qDAA2E;AAE3E,uDAI2B;AAC3B,2DAAmD;AAgBnD;;;;;;;;;;;GAWG;AACH,MAAa,0BAA0B;IACrC,SAAS,GAAG,IAAI,GAAG,EAGhB,CAAC;IAEJ,GAAG,CAAC,IAAY;QACd,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QACzC,OAAO,OAAO,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,SAAS,CAAC;IAC1C,CAAC;IAED;;;;;;;;;;;;;;;;;;OAkBG;IACH,QAAQ,CAAC,IAAY,EAAE,OAA2B;QAChD,IAAA,kBAAM,EACJ,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,EACzB,0CAA0C,IAAI,EAAE,CACjD,CAAC;QACF,IAAA,kBAAM,EACJ,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,EACxB,sDAAsD,CACvD,CAAC;QACF,IAAA,kBAAM,EACJ,OAAO,CAAC,QAAQ,IAAI,OAAO,CAAC,QAAQ,EACpC,gDAAgD,CACjD,CAAC;QAEF,MAAM,OAAO,GAAG,KAAM,SAAQ,8BAAY;YACxC,MAAM,CAAU,gBAAgB,GAAqB,IAAA,iCAAmB,EACtE,CAAC,IAAI,EAAE,QAAQ,EAAE,aAAa,EAAE,EAAE;gBAChC,OAAO,aAAa,CAAC,oBAAoB;qBACtC,GAAG,CAAC,WAAW,CAAC,MAAM,CAAC,CAAE;qBACzB,gBAAgB,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;YACtC,CAAC,EACD,EAAC,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAC,CAC7B,CAAC;YACF,MAAM,CAAU,aAAa,GAAkB,IAAA,iCAAmB,EAChE,CAAC,IAAI,EAAE,QAAQ,EAAE,aAAa,EAAE,EAAE;gBAChC,OAAO,aAAa,CAAC,oBAAoB;qBACtC,GAAG,CAAC,WAAW,CAAC,MAAM,CAAC,CAAE;qBACzB,aAAa,CAAC,IAAI,EAAE,QAAQ,CAAC,CAAC;YACnC,CAAC,EACD,EAAC,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC,EAAC,CAC7B,CAAC;SACH,CAAC;QACF,MAAM,cAAc,GAAG,IAAA,iCAAmB,EACxC,CAAC,aAA4B,EAAE,EAAE;YAC/B,aAAa,CAAC,oBAAoB,CAAC,QAAQ,CAAC,WAAW,CAAC,MAAM,CAAC,EAAE;gBAC/D,QAAQ,EAAE,WAAW,CAAC,UAAU,CAAC;gBACjC,QAAQ,EAAE,WAAW,CAAC,UAAU,CAAC;aAClC,CAAC,CAAC;QACL,CAAC,EACD;YACE,IAAI,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI,CAAC;YAC1B,QAAQ,EAAE,OAAO,CAAC,QAAQ;gBACxB,CAAC,CAAC,IAAA,+BAAiB,EAAC,OAAO,CAAC,QAAQ,CAAC;gBACrC,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC;YACrB,QAAQ,EAAE,OAAO,CAAC,QAAQ;gBACxB,CAAC,CAAC,IAAA,+BAAiB,EAAC,OAAO,CAAC,QAAQ,CAAC;gBACrC,CAAC,CAAC,MAAM,CAAC,SAAS,CAAC;SACtB,CACF,CAAC,QAAQ,EAAE,CAAC;QAEb,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,EAAE,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC,CAAC;QACpD,kCAAc,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC;IACxC,CAAC;IAED;;;;;OAKG;IACH,UAAU,CAAC,IAAY;QACrB,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;QACzC,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,MAAM,IAAI,KAAK,CAAC,sCAAsC,IAAI,EAAE,CAAC,CAAC;QAChE,CAAC;QACD,kCAAc,CAAC,GAAG,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;QAC/B,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;IAC9B,CAAC;IAED;;OAEG;IACH,KAAK;QACH,OAAO,CAAC,GAAG,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,CAAC,CAAC;IACpC,CAAC;IAED;;OAEG;IACH,KAAK;QACH,KAAK,MAAM,CAAC,cAAc,CAAC,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YAC9C,kCAAc,CAAC,GAAG,CAAC,cAAc,CAAC,CAAC;QACrC,CAAC;QACD,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,CAAC;IACzB,CAAC;CACF;AAnHD,gEAmHC;AAED;;GAEG;AACU,QAAA,mBAAmB,GAAG,IAAI,0BAA0B,EAAE,CAAC;AAEpE;;;;;GAKG;AACH,SAAgB,0BAA0B,CACxC,IAAY,EACZ,OAA2B;IAE3B,2BAAmB,CAAC,QAAQ,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;AAC9C,CAAC;AALD,gEAKC;AAED;;;;;GAKG;AACH,SAAgB,4BAA4B,CAAC,IAAY;IACvD,2BAAmB,CAAC,UAAU,CAAC,IAAI,CAAC,CAAC;AACvC,CAAC;AAFD,oEAEC;AAED;;;;;GAKG;AACH,SAAgB,uBAAuB;IACrC,OAAO,2BAAmB,CAAC,KAAK,EAAE,CAAC;AACrC,CAAC;AAFD,0DAEC;AAED;;;;;GAKG;AACH,SAAgB,wBAAwB;IACtC,2BAAmB,CAAC,KAAK,EAAE,CAAC;AAC9B,CAAC;AAFD,4DAEC"}