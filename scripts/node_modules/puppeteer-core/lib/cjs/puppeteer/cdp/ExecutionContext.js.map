{"version": 3, "file": "ExecutionContext.js", "sourceRoot": "", "sources": ["../../../../src/cdp/ExecutionContext.ts"], "names": [], "mappings": ";AAAA;;;;GAIG;;;AAOH,qDAA6C;AAC7C,mEAA2D;AAE3D,+CAM2B;AAE3B,uEAA+D;AAC/D,qDAAsD;AAEtD,+DAAuD;AACvD,6CAAqC;AACrC,yDAAoD;AAEpD,+CAA0C;AAC1C,yCAAwE;AAExE;;GAEG;AACH,MAAa,gBAAgB;IAC3B,OAAO,CAAa;IACpB,MAAM,CAAgB;IACtB,UAAU,CAAS;IACnB,YAAY,CAAU;IAEtB,YACE,MAAkB,EAClB,cAA4D,EAC5D,KAAoB;QAEpB,IAAI,CAAC,OAAO,GAAG,MAAM,CAAC;QACtB,IAAI,CAAC,MAAM,GAAG,KAAK,CAAC;QACpB,IAAI,CAAC,UAAU,GAAG,cAAc,CAAC,EAAE,CAAC;QACpC,IAAI,cAAc,CAAC,IAAI,EAAE,CAAC;YACxB,IAAI,CAAC,YAAY,GAAG,cAAc,CAAC,IAAI,CAAC;QAC1C,CAAC;IACH,CAAC;IAED,kBAAkB,GAAG,KAAK,CAAC;IAC3B,cAAc,CAAoC;IAClD,IAAI,aAAa;QACf,IAAI,OAAO,GAAG,OAAO,CAAC,OAAO,EAAsB,CAAC;QACpD,IAAI,CAAC,IAAI,CAAC,kBAAkB,EAAE,CAAC;YAC7B,OAAO,GAAG,OAAO,CAAC,GAAG,CAAC;gBACpB,IAAI,CAAC,qBAAqB,CACxB,IAAI,oBAAO,CACT,qBAAqB,EACrB,sCAAgB,CAAC,QAA2C,CAC7D,CACF;gBACD,IAAI,CAAC,qBAAqB,CACxB,IAAI,oBAAO,CAAC,wBAAwB,EAAE,CAAC,KAAK,EAC1C,OAA4B,EAC5B,QAAgB,EACW,EAAE;oBAC7B,MAAM,OAAO,GAAG,sCAAgB,CAAC,QAAQ,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;oBAC7D,OAAO,MAAM,OAAO,CAAC,KAAK,CAAC,cAAc,CACvC,CAAC,GAAG,QAAQ,EAAE,EAAE;wBACd,OAAO,QAAQ,CAAC;oBAClB,CAAC,EACD,GAAG,CAAC,MAAM,wCAAiB,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAC9C,CAAC;gBACJ,CAAC,CAAoC,CAAC,CACvC;aACF,CAAC,CAAC;YACH,IAAI,CAAC,kBAAkB,GAAG,IAAI,CAAC;QACjC,CAAC;QACD,kCAAc,CAAC,MAAM,CAAC,MAAM,CAAC,EAAE;YAC7B,IAAI,IAAI,CAAC,cAAc,EAAE,CAAC;gBACxB,KAAK,IAAI,CAAC,cAAc,CAAC,IAAI,CAAC,MAAM,CAAC,EAAE;oBACrC,KAAK,MAAM,CAAC,OAAO,EAAE,CAAC;gBACxB,CAAC,CAAC,CAAC;YACL,CAAC;YACD,IAAI,CAAC,cAAc,GAAG,OAAO,CAAC,IAAI,CAAC,GAAG,EAAE;gBACtC,OAAO,IAAI,CAAC,cAAc,CAAC,MAAM,CAAqC,CAAC;YACzE,CAAC,CAAC,CAAC;QACL,CAAC,EAAE,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QACzB,OAAO,IAAI,CAAC,cAAkD,CAAC;IACjE,CAAC;IAED,KAAK,CAAC,qBAAqB,CAAC,OAAgB;QAC1C,IAAI,CAAC;YACH,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC;gBAChB,IAAI,CAAC,MAAM,CAAC,SAAS,CAAC,GAAG,CAAC,OAAO,CAAC,IAAI,EAAE,OAAO,CAAC,CAAC;gBACjD,MAAM,IAAI,CAAC,MAAM,CAAC,oBAAoB,CAAC,IAAI,EAAE,OAAO,CAAC,IAAI,CAAC,CAAC;YAC7D,CAAC;QACH,CAAC;QAAC,MAAM,CAAC;YACP,0EAA0E;YAC1E,uEAAuE;YACvE,gCAAgC;QAClC,CAAC;IACH,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAuCG;IACH,KAAK,CAAC,QAAQ,CAIZ,YAA2B,EAC3B,GAAG,IAAY;QAEf,OAAO,MAAM,IAAI,CAAC,SAAS,CAAC,IAAI,EAAE,YAAY,EAAE,GAAG,IAAI,CAAC,CAAC;IAC3D,CAAC;IAED;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;OAgDG;IACH,KAAK,CAAC,cAAc,CAIlB,YAA2B,EAC3B,GAAG,IAAY;QAEf,OAAO,MAAM,IAAI,CAAC,SAAS,CAAC,KAAK,EAAE,YAAY,EAAE,GAAG,IAAI,CAAC,CAAC;IAC5D,CAAC;IAkBD,KAAK,CAAC,SAAS,CAIb,aAAsB,EACtB,YAA2B,EAC3B,GAAG,IAAY;QAEf,MAAM,gBAAgB,GAAG,IAAA,6BAAmB,EAC1C,IAAA,0CAAgC,EAAC,YAAY,CAAC,EAAE,QAAQ,EAAE;YACxD,sBAAY,CAAC,YAAY,CAC5B,CAAC;QAEF,IAAI,IAAA,kBAAQ,EAAC,YAAY,CAAC,EAAE,CAAC;YAC3B,MAAM,SAAS,GAAG,IAAI,CAAC,UAAU,CAAC;YAClC,MAAM,UAAU,GAAG,YAAY,CAAC;YAChC,MAAM,uBAAuB,GAAG,0BAAgB,CAAC,IAAI,CAAC,UAAU,CAAC;gBAC/D,CAAC,CAAC,UAAU;gBACZ,CAAC,CAAC,GAAG,UAAU,KAAK,gBAAgB,IAAI,CAAC;YAE3C,MAAM,EAAC,gBAAgB,EAAE,MAAM,EAAE,YAAY,EAAC,GAAG,MAAM,IAAI,CAAC,OAAO;iBAChE,IAAI,CAAC,kBAAkB,EAAE;gBACxB,UAAU,EAAE,uBAAuB;gBACnC,SAAS;gBACT,aAAa;gBACb,YAAY,EAAE,IAAI;gBAClB,WAAW,EAAE,IAAI;aAClB,CAAC;iBACD,KAAK,CAAC,YAAY,CAAC,CAAC;YAEvB,IAAI,gBAAgB,EAAE,CAAC;gBACrB,MAAM,IAAA,gCAAqB,EAAC,gBAAgB,CAAC,CAAC;YAChD,CAAC;YAED,OAAO,aAAa;gBAClB,CAAC,CAAC,IAAA,gCAAqB,EAAC,YAAY,CAAC;gBACrC,CAAC,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;QACjD,CAAC;QAED,MAAM,mBAAmB,GAAG,IAAA,+BAAiB,EAAC,YAAY,CAAC,CAAC;QAC5D,MAAM,gCAAgC,GAAG,0BAAgB,CAAC,IAAI,CAC5D,mBAAmB,CACpB;YACC,CAAC,CAAC,mBAAmB;YACrB,CAAC,CAAC,GAAG,mBAAmB,KAAK,gBAAgB,IAAI,CAAC;QACpD,IAAI,qBAAqB,CAAC;QAC1B,IAAI,CAAC;YACH,qBAAqB,GAAG,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,wBAAwB,EAAE;gBAClE,mBAAmB,EAAE,gCAAgC;gBACrD,kBAAkB,EAAE,IAAI,CAAC,UAAU;gBACnC,SAAS,EAAE,IAAI,CAAC,MAAM;oBACpB,CAAC,CAAC,MAAM,OAAO,CAAC,GAAG,CAAC,IAAI,CAAC,GAAG,CAAC,eAAe,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;oBACzD,CAAC,CAAC,EAAE;gBACN,aAAa;gBACb,YAAY,EAAE,IAAI;gBAClB,WAAW,EAAE,IAAI;aAClB,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,IACE,KAAK,YAAY,SAAS;gBAC1B,KAAK,CAAC,OAAO,CAAC,UAAU,CAAC,uCAAuC,CAAC,EACjE,CAAC;gBACD,KAAK,CAAC,OAAO,IAAI,qCAAqC,CAAC;YACzD,CAAC;YACD,MAAM,KAAK,CAAC;QACd,CAAC;QACD,MAAM,EAAC,gBAAgB,EAAE,MAAM,EAAE,YAAY,EAAC,GAC5C,MAAM,qBAAqB,CAAC,KAAK,CAAC,YAAY,CAAC,CAAC;QAClD,IAAI,gBAAgB,EAAE,CAAC;YACrB,MAAM,IAAA,gCAAqB,EAAC,gBAAgB,CAAC,CAAC;QAChD,CAAC;QACD,OAAO,aAAa;YAClB,CAAC,CAAC,IAAA,gCAAqB,EAAC,YAAY,CAAC;YACrC,CAAC,CAAC,eAAe,CAAC,IAAI,CAAC,MAAM,EAAE,YAAY,CAAC,CAAC;QAE/C,KAAK,UAAU,eAAe,CAE5B,GAAY;YAEZ,IAAI,GAAG,YAAY,oBAAO,EAAE,CAAC;gBAC3B,GAAG,GAAG,MAAM,GAAG,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC;YAC5B,CAAC;YACD,IAAI,OAAO,GAAG,KAAK,QAAQ,EAAE,CAAC;gBAC5B,mCAAmC;gBACnC,OAAO,EAAC,mBAAmB,EAAE,GAAG,GAAG,CAAC,QAAQ,EAAE,GAAG,EAAC,CAAC;YACrD,CAAC;YACD,IAAI,MAAM,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;gBACvB,OAAO,EAAC,mBAAmB,EAAE,IAAI,EAAC,CAAC;YACrC,CAAC;YACD,IAAI,MAAM,CAAC,EAAE,CAAC,GAAG,EAAE,QAAQ,CAAC,EAAE,CAAC;gBAC7B,OAAO,EAAC,mBAAmB,EAAE,UAAU,EAAC,CAAC;YAC3C,CAAC;YACD,IAAI,MAAM,CAAC,EAAE,CAAC,GAAG,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC;gBAC9B,OAAO,EAAC,mBAAmB,EAAE,WAAW,EAAC,CAAC;YAC5C,CAAC;YACD,IAAI,MAAM,CAAC,EAAE,CAAC,GAAG,EAAE,GAAG,CAAC,EAAE,CAAC;gBACxB,OAAO,EAAC,mBAAmB,EAAE,KAAK,EAAC,CAAC;YACtC,CAAC;YACD,MAAM,YAAY,GAChB,GAAG,IAAI,CAAC,GAAG,YAAY,yBAAW,IAAI,GAAG,YAAY,mCAAgB,CAAC;gBACpE,CAAC,CAAC,GAAG;gBACL,CAAC,CAAC,IAAI,CAAC;YACX,IAAI,YAAY,EAAE,CAAC;gBACjB,IAAI,YAAY,CAAC,KAAK,KAAK,IAAI,CAAC,MAAM,EAAE,CAAC;oBACvC,MAAM,IAAI,KAAK,CACb,mEAAmE,CACpE,CAAC;gBACJ,CAAC;gBACD,IAAI,YAAY,CAAC,QAAQ,EAAE,CAAC;oBAC1B,MAAM,IAAI,KAAK,CAAC,uBAAuB,CAAC,CAAC;gBAC3C,CAAC;gBACD,IAAI,YAAY,CAAC,YAAY,EAAE,CAAC,mBAAmB,EAAE,CAAC;oBACpD,OAAO;wBACL,mBAAmB,EACjB,YAAY,CAAC,YAAY,EAAE,CAAC,mBAAmB;qBAClD,CAAC;gBACJ,CAAC;gBACD,IAAI,CAAC,YAAY,CAAC,YAAY,EAAE,CAAC,QAAQ,EAAE,CAAC;oBAC1C,OAAO,EAAC,KAAK,EAAE,YAAY,CAAC,YAAY,EAAE,CAAC,KAAK,EAAC,CAAC;gBACpD,CAAC;gBACD,OAAO,EAAC,QAAQ,EAAE,YAAY,CAAC,YAAY,EAAE,CAAC,QAAQ,EAAC,CAAC;YAC1D,CAAC;YACD,OAAO,EAAC,KAAK,EAAE,GAAG,EAAC,CAAC;QACtB,CAAC;IACH,CAAC;CACF;AApUD,4CAoUC;AAED,MAAM,YAAY,GAAG,CAAC,KAAY,EAAqC,EAAE;IACvE,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,oCAAoC,CAAC,EAAE,CAAC;QACjE,OAAO,EAAC,MAAM,EAAE,EAAC,IAAI,EAAE,WAAW,EAAC,EAAC,CAAC;IACvC,CAAC;IACD,IAAI,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,sCAAsC,CAAC,EAAE,CAAC;QACnE,OAAO,EAAC,MAAM,EAAE,EAAC,IAAI,EAAE,WAAW,EAAC,EAAC,CAAC;IACvC,CAAC;IAED,IACE,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,uCAAuC,CAAC;QAC/D,KAAK,CAAC,OAAO,CAAC,QAAQ,CAAC,sCAAsC,CAAC,EAC9D,CAAC;QACD,MAAM,IAAI,KAAK,CACb,uEAAuE,CACxE,CAAC;IACJ,CAAC;IACD,MAAM,KAAK,CAAC;AACd,CAAC,CAAC;AAEF;;GAEG;AACH,SAAgB,eAAe,CAC7B,KAAoB,EACpB,YAA2C;IAE3C,IAAI,YAAY,CAAC,OAAO,KAAK,MAAM,EAAE,CAAC;QACpC,OAAO,IAAI,mCAAgB,CAAC,KAAK,EAAE,YAAY,CAAC,CAAC;IACnD,CAAC;IACD,OAAO,IAAI,yBAAW,CAAC,KAAK,EAAE,YAAY,CAAC,CAAC;AAC9C,CAAC;AARD,0CAQC"}