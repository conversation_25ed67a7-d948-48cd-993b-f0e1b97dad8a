"use strict";
var __defProp = Object.defineProperty;
var __getOwnPropDesc = Object.getOwnPropertyDescriptor;
var __getOwnPropNames = Object.getOwnPropertyNames;
var __hasOwnProp = Object.prototype.hasOwnProperty;
var __export = (target, all) => {
  for (var name in all)
    __defProp(target, name, { get: all[name], enumerable: true });
};
var __copyProps = (to, from, except, desc) => {
  if (from && typeof from === "object" || typeof from === "function") {
    for (let key of __getOwnPropNames(from))
      if (!__hasOwnProp.call(to, key) && key !== except)
        __defProp(to, key, { get: () => from[key], enumerable: !(desc = __getOwnPropDesc(from, key)) || desc.enumerable });
  }
  return to;
};
var __toCommonJS = (mod) => __copyProps(__defProp({}, "__esModule", { value: true }), mod);
var uiModeReporter_exports = {};
__export(uiModeReporter_exports, {
  default: () => uiModeReporter_default
});
module.exports = __toCommonJS(uiModeReporter_exports);
var import_teleEmitter = require("../reporters/teleEmitter");
class UIModeReporter extends import_teleEmitter.TeleReporterEmitter {
  constructor(options) {
    super(options._send, { omitBuffers: true });
  }
}
var uiModeReporter_default = UIModeReporter;
