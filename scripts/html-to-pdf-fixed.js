const { chromium } = require('playwright');
const fs = require('fs');
const path = require('path');
const { JSDOM } = require('jsdom');

// 配置常量
const CONFIG = {
    baseDir: '/Users/<USER>/Desktop/Trea ai /data/help/help.kumanyun.com',
    outputDir: '/Users/<USER>/Desktop/Trea ai /data/help/pdf-fixed',
    mainFiles: [
        'help-4.html',  // 网站后台
        'help-5.html',  // 功能详解
        'help-7.html'   // 二次开发
    ],
    pdfOptions: {
        format: 'A4',
        printBackground: true,
        margin: {
            top: '20px',
            right: '20px',
            bottom: '20px',
            left: '20px'
        }
    }
};

/**
 * 提取HTML文件的正文内容并修复图片路径
 */
function extractContentAndFixImages(htmlFilePath) {
    try {
        const htmlContent = fs.readFileSync(htmlFilePath, 'utf8');
        const dom = new JSDOM(htmlContent);
        const document = dom.window.document;
        
        // 提取页面标题
        const titleElement = document.querySelector('.content h3.title');
        const pageTitle = titleElement ? titleElement.textContent.trim() : '未知标题';
        
        // 提取面包屑导航
        const breadElement = document.querySelector('.bread');
        const breadcrumb = breadElement ? breadElement.textContent.trim() : '';
        
        // 提取主要内容区域
        const contentElement = document.querySelector('.content');
        if (!contentElement) {
            throw new Error('未找到内容区域');
        }
        
        // 修复图片路径
        const images = contentElement.querySelectorAll('img');
        images.forEach(img => {
            const src = img.getAttribute('src');
            if (src && src.startsWith('upload/')) {
                // 转换为绝对路径
                const absolutePath = `file://${CONFIG.baseDir}/${src}`;
                img.setAttribute('src', absolutePath);

                // 添加图片样式确保正确显示
                img.setAttribute('style', 'max-width: 100%; height: auto; display: block; margin: 10px 0;');
            }
        });
        
        // 修复链接路径
        const links = contentElement.querySelectorAll('a[href^="help-"]');
        links.forEach(link => {
            const href = link.getAttribute('href');
            if (href) {
                const absolutePath = `file://${CONFIG.baseDir}/${href}`;
                link.setAttribute('href', absolutePath);
            }
        });
        
        // 创建简化的HTML结构，只包含内容
        const cleanHtml = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${pageTitle}</title>
    <style>
        body {
            font-family: "Microsoft YaHei", "PingFang SC", "Helvetica Neue", Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #fff;
        }
        .breadcrumb {
            font-size: 14px;
            color: #666;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }
        .title {
            font-size: 24px;
            font-weight: bold;
            color: #333;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #007bff;
        }
        .content {
            font-size: 15px;
            line-height: 1.8;
        }
        .content img {
            max-width: 100%;
            height: auto;
            margin: 10px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .content p {
            margin: 10px 0;
        }
        .content strong {
            color: #007bff;
            font-weight: bold;
        }
        .content a {
            color: #007bff;
            text-decoration: none;
        }
        .content a:hover {
            text-decoration: underline;
        }
        .lake-content {
            margin: 15px 0;
        }
        .ne-p {
            margin: 8px 0;
            line-height: 1.6;
        }
        .highlight {
            background-color: #fff3cd;
            padding: 2px 4px;
            border-radius: 2px;
        }
    </style>
</head>
<body>
    <div class="breadcrumb">${breadcrumb}</div>
    <h1 class="title">${pageTitle}</h1>
    <div class="content">
        ${contentElement.innerHTML}
    </div>
</body>
</html>`;
        
        return {
            title: pageTitle,
            breadcrumb: breadcrumb,
            content: cleanHtml,
            hasImages: images.length > 0,
            imageCount: images.length
        };
        
    } catch (error) {
        console.error(`提取内容失败: ${htmlFilePath}`, error);
        return null;
    }
}

/**
 * 转换单个HTML文件为PDF
 */
async function convertHtmlToPdf(fileInfo, outputPath, browser) {
    try {
        console.log(`开始转换: ${fileInfo.title || fileInfo.filename}`);
        
        const page = await browser.newPage();
        
        // 设置视口大小
        await page.setViewportSize({ width: 1200, height: 800 });
        
        // 提取并修复内容
        const contentData = extractContentAndFixImages(fileInfo.path);
        if (!contentData) {
            throw new Error('无法提取页面内容');
        }
        
        console.log(`  标题: ${contentData.title}`);
        console.log(`  图片数量: ${contentData.imageCount}`);
        
        // 设置页面内容
        await page.setContent(contentData.content, {
            waitUntil: 'networkidle',
            timeout: 30000
        });
        
        // 等待图片加载
        if (contentData.hasImages) {
            console.log(`  等待 ${contentData.imageCount} 张图片加载...`);

            // 等待页面稳定
            await page.waitForTimeout(2000);

            // 检查图片加载状态
            const imageLoadResults = await page.evaluate(() => {
                const images = Array.from(document.images);
                const results = [];

                return Promise.all(
                    images.map((img, index) => {
                        return new Promise((resolve) => {
                            if (img.complete && img.naturalWidth > 0) {
                                results.push({ index, status: 'loaded', src: img.src });
                                resolve();
                            } else {
                                const timeout = setTimeout(() => {
                                    results.push({ index, status: 'timeout', src: img.src });
                                    resolve();
                                }, 8000); // 8秒超时

                                img.addEventListener('load', () => {
                                    clearTimeout(timeout);
                                    results.push({ index, status: 'loaded', src: img.src });
                                    resolve();
                                });

                                img.addEventListener('error', () => {
                                    clearTimeout(timeout);
                                    results.push({ index, status: 'error', src: img.src });
                                    resolve();
                                });
                            }
                        });
                    })
                ).then(() => results);
            });

            console.log(`  图片加载完成: ${imageLoadResults.filter(r => r.status === 'loaded').length}/${imageLoadResults.length}`);
        }
        
        // 生成PDF
        await page.pdf({
            path: outputPath,
            ...CONFIG.pdfOptions
        });
        
        await page.close();
        
        // 检查文件大小
        const stats = fs.statSync(outputPath);
        const fileSizeMB = (stats.size / 1024 / 1024).toFixed(2);
        
        console.log(`  ✓ 成功生成PDF (${fileSizeMB} MB)`);
        
        return { 
            success: true, 
            fileSize: fileSizeMB,
            title: contentData.title,
            imageCount: contentData.imageCount
        };
        
    } catch (error) {
        console.error(`  ✗ 转换失败: ${error.message}`);
        throw error;
    }
}

/**
 * 测试修复后的转换功能
 */
async function testFixedConversion() {
    console.log('火鸟门户帮助文档 修复版PDF转换测试');
    console.log('=====================================');
    
    // 创建输出目录
    if (!fs.existsSync(CONFIG.outputDir)) {
        fs.mkdirSync(CONFIG.outputDir, { recursive: true });
    }
    
    // 测试文件列表
    const testFiles = [
        'help-4.html',          // 主页面
        'help-246-800.html',    // 系统基本参数（有大量图片）
        'help-238-799.html',    // 另一个文章页面
        'help-239-801.html'     // 用户相关页面
    ];
    
    // 启动浏览器
    console.log('启动浏览器...');
    const browser = await chromium.launch({
        headless: true
    });
    
    let successCount = 0;
    let failCount = 0;
    const results = [];
    
    try {
        for (let i = 0; i < testFiles.length; i++) {
            const testFile = testFiles[i];
            const inputPath = path.join(CONFIG.baseDir, testFile);
            const outputFileName = `fixed_${testFile.replace('.html', '.pdf')}`;
            const outputPath = path.join(CONFIG.outputDir, outputFileName);
            
            if (!fs.existsSync(inputPath)) {
                console.log(`[${i + 1}/${testFiles.length}] 文件不存在: ${testFile}`);
                failCount++;
                continue;
            }
            
            try {
                console.log(`\n[${i + 1}/${testFiles.length}] 处理: ${testFile}`);
                
                const fileInfo = {
                    filename: testFile,
                    path: inputPath
                };
                
                const result = await convertHtmlToPdf(fileInfo, outputPath, browser);
                
                results.push({
                    file: testFile,
                    status: 'success',
                    ...result
                });
                
                successCount++;
                
            } catch (error) {
                console.error(`转换失败: ${testFile} - ${error.message}`);
                
                results.push({
                    file: testFile,
                    status: 'failed',
                    error: error.message
                });
                
                failCount++;
            }
            
            // 添加延迟
            if (i < testFiles.length - 1) {
                await new Promise(resolve => setTimeout(resolve, 2000));
            }
        }
        
    } finally {
        await browser.close();
    }
    
    // 保存测试结果
    const reportFile = path.join(CONFIG.outputDir, 'test_results.json');
    fs.writeFileSync(reportFile, JSON.stringify({
        timestamp: new Date().toISOString(),
        totalFiles: testFiles.length,
        successCount: successCount,
        failCount: failCount,
        results: results
    }, null, 2));
    
    console.log('\n=====================================');
    console.log('测试完成统计:');
    console.log(`总文件数: ${testFiles.length}`);
    console.log(`成功: ${successCount}`);
    console.log(`失败: ${failCount}`);
    console.log(`输出目录: ${CONFIG.outputDir}`);
    console.log(`测试报告: ${reportFile}`);
    
    // 显示成功转换的文件详情
    if (successCount > 0) {
        console.log('\n成功转换的文件:');
        results.filter(r => r.status === 'success').forEach(r => {
            console.log(`  ${r.file}: ${r.title} (${r.imageCount} 张图片, ${r.fileSize} MB)`);
        });
    }
}

// 执行测试
if (require.main === module) {
    testFixedConversion().catch(error => {
        console.error('测试失败:', error);
        process.exit(1);
    });
}
