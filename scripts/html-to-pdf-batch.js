const { chromium } = require('playwright');
const fs = require('fs');
const path = require('path');
const { JSDOM } = require('jsdom');

// 配置常量
const CONFIG = {
    baseDir: '/Users/<USER>/Desktop/Trea ai /data/help/help.kumanyun.com',
    outputDir: '/Users/<USER>/Desktop/Trea ai /data/help/pdf',
    mainFiles: [
        'help-4.html',  // 网站后台
        'help-5.html',  // 功能详解
        'help-7.html'   // 二次开发
    ],
    pdfOptions: {
        format: 'A4',
        printBackground: true,
        margin: {
            top: '20px',
            right: '20px',
            bottom: '20px',
            left: '20px'
        }
    },
    // 批量处理配置
    batchSize: 5,  // 每批处理的文件数
    delayBetweenFiles: 1000,  // 文件间延迟(ms)
    delayBetweenBatches: 3000,  // 批次间延迟(ms)
    maxRetries: 2  // 最大重试次数
};

/**
 * 从HTML文件中提取所有相关链接
 */
function extractLinksFromHtml(htmlFilePath) {
    try {
        const htmlContent = fs.readFileSync(htmlFilePath, 'utf8');
        const dom = new JSDOM(htmlContent);
        const document = dom.window.document;
        
        const links = [];
        const menuLinks = document.querySelectorAll('.menu-list a[href^="help-"]');
        
        menuLinks.forEach(link => {
            const href = link.getAttribute('href');
            const title = link.textContent.trim();
            const level = getElementLevel(link);
            
            if (href && href.endsWith('.html')) {
                links.push({
                    href: href,
                    title: title,
                    level: level,
                    type: link.classList.contains('article') ? 'article' : 'category'
                });
            }
        });
        
        return links;
    } catch (error) {
        console.error(`提取链接失败: ${htmlFilePath}`, error);
        return [];
    }
}

/**
 * 获取元素的层级
 */
function getElementLevel(element) {
    let level = 0;
    let current = element;
    
    while (current) {
        if (current.classList && (
            current.classList.contains('level1') ||
            current.classList.contains('level2') ||
            current.classList.contains('level3') ||
            current.classList.contains('level4') ||
            current.classList.contains('level5')
        )) {
            const levelClass = Array.from(current.classList).find(cls => cls.startsWith('level'));
            level = parseInt(levelClass.replace('level', ''));
            break;
        }
        current = current.parentElement;
    }
    
    return level;
}

/**
 * 获取主文件标题
 */
function getMainFileTitle(filename) {
    const titles = {
        'help-4.html': '网站后台',
        'help-5.html': '功能详解', 
        'help-7.html': '二次开发'
    };
    return titles[filename] || filename;
}

/**
 * 获取所有相关的HTML文件列表
 */
function getAllRelatedFiles(mainFile) {
    const mainFilePath = path.join(CONFIG.baseDir, mainFile);
    const links = extractLinksFromHtml(mainFilePath);
    
    const files = [];
    
    // 添加主文件
    files.push({
        filename: mainFile,
        path: path.join(CONFIG.baseDir, mainFile),
        title: getMainFileTitle(mainFile),
        level: 0,
        type: 'main'
    });
    
    // 添加所有子页面文件，按层级和类型过滤
    links.forEach((link, index) => {
        const filePath = path.join(CONFIG.baseDir, link.href);
        if (fs.existsSync(filePath)) {
            files.push({
                filename: link.href,
                path: filePath,
                title: link.title,
                level: link.level,
                type: link.type,
                index: index + 1
            });
        }
    });
    
    return files;
}

/**
 * 转换单个HTML文件为PDF
 */
async function convertHtmlToPdf(fileInfo, outputPath, browser, retryCount = 0) {
    try {
        const page = await browser.newPage();
        
        // 设置视口大小
        await page.setViewportSize({ width: 1200, height: 800 });
        
        // 读取HTML文件内容
        const htmlContent = fs.readFileSync(fileInfo.path, 'utf8');
        
        // 修复相对路径，使图片和CSS能正确加载
        const baseUrl = `file://${CONFIG.baseDir}/`;
        const fixedContent = htmlContent.replace(
            /(src|href)="(?!http|\/\/|data:)([^"]+)"/g,
            `$1="${baseUrl}$2"`
        );
        
        // 设置页面内容
        await page.setContent(fixedContent, {
            waitUntil: 'networkidle',
            timeout: 30000
        });
        
        // 等待图片加载
        await page.evaluate(() => {
            return Promise.all(
                Array.from(document.images, img => {
                    if (img.complete) return Promise.resolve();
                    return new Promise((resolve) => {
                        img.addEventListener('load', resolve);
                        img.addEventListener('error', resolve);
                        setTimeout(resolve, 3000); // 3秒超时
                    });
                })
            );
        });
        
        // 生成PDF
        await page.pdf({
            path: outputPath,
            ...CONFIG.pdfOptions
        });
        
        await page.close();
        
        // 检查文件大小
        const stats = fs.statSync(outputPath);
        const fileSizeMB = (stats.size / 1024 / 1024).toFixed(2);
        
        return { success: true, fileSize: fileSizeMB };
        
    } catch (error) {
        if (retryCount < CONFIG.maxRetries) {
            console.log(`  重试 ${retryCount + 1}/${CONFIG.maxRetries}...`);
            await new Promise(resolve => setTimeout(resolve, 2000));
            return await convertHtmlToPdf(fileInfo, outputPath, browser, retryCount + 1);
        }
        throw error;
    }
}

/**
 * 批量转换单个主文档的所有相关文件
 */
async function convertMainDocument(mainFile, browser) {
    const mainTitle = getMainFileTitle(mainFile);
    console.log(`\n开始处理: ${mainTitle} (${mainFile})`);
    console.log('='.repeat(50));

    // 创建输出目录
    const outputDir = path.join(CONFIG.outputDir, mainTitle);
    if (!fs.existsSync(outputDir)) {
        fs.mkdirSync(outputDir, { recursive: true });
    }

    // 获取所有相关文件
    const files = getAllRelatedFiles(mainFile);
    console.log(`找到 ${files.length} 个相关文件`);

    let successCount = 0;
    let failCount = 0;
    const results = [];

    // 分批处理文件
    for (let i = 0; i < files.length; i += CONFIG.batchSize) {
        const batch = files.slice(i, i + CONFIG.batchSize);
        const batchNum = Math.floor(i / CONFIG.batchSize) + 1;
        const totalBatches = Math.ceil(files.length / CONFIG.batchSize);

        console.log(`\n批次 ${batchNum}/${totalBatches} (${batch.length} 个文件):`);

        // 处理当前批次的每个文件
        for (let j = 0; j < batch.length; j++) {
            const file = batch[j];
            const fileIndex = i + j + 1;
            const outputFileName = `${String(fileIndex).padStart(3, '0')}_${file.title.replace(/[\/\\:*?"<>|]/g, '_')}.pdf`;
            const outputPath = path.join(outputDir, outputFileName);

            try {
                console.log(`[${fileIndex}/${files.length}] ${file.title}`);
                const result = await convertHtmlToPdf(file, outputPath, browser);
                console.log(`  ✓ 成功 (${result.fileSize} MB)`);

                results.push({
                    file: file.filename,
                    title: file.title,
                    status: 'success',
                    fileSize: result.fileSize,
                    outputPath: outputPath
                });

                successCount++;
            } catch (error) {
                console.error(`  ✗ 失败: ${error.message}`);

                results.push({
                    file: file.filename,
                    title: file.title,
                    status: 'failed',
                    error: error.message
                });

                failCount++;
            }

            // 文件间延迟
            if (j < batch.length - 1) {
                await new Promise(resolve => setTimeout(resolve, CONFIG.delayBetweenFiles));
            }
        }

        // 批次间延迟
        if (i + CONFIG.batchSize < files.length) {
            console.log(`  等待 ${CONFIG.delayBetweenBatches/1000} 秒后处理下一批...`);
            await new Promise(resolve => setTimeout(resolve, CONFIG.delayBetweenBatches));
        }
    }

    // 保存处理结果
    const resultFile = path.join(outputDir, 'conversion_results.json');
    fs.writeFileSync(resultFile, JSON.stringify({
        mainFile: mainFile,
        mainTitle: mainTitle,
        totalFiles: files.length,
        successCount: successCount,
        failCount: failCount,
        results: results,
        timestamp: new Date().toISOString()
    }, null, 2));

    console.log(`\n${mainTitle} 处理完成:`);
    console.log(`  成功: ${successCount}`);
    console.log(`  失败: ${failCount}`);
    console.log(`  结果文件: ${resultFile}`);

    return { success: successCount, failed: failCount, results: results };
}

/**
 * 主函数 - 批量转换所有文档
 */
async function main() {
    console.log('火鸟门户帮助文档 HTML转PDF 批量转换工具');
    console.log('==========================================');
    console.log(`基础目录: ${CONFIG.baseDir}`);
    console.log(`输出目录: ${CONFIG.outputDir}`);
    console.log(`批次大小: ${CONFIG.batchSize} 个文件/批`);
    console.log(`文件间延迟: ${CONFIG.delayBetweenFiles}ms`);
    console.log(`批次间延迟: ${CONFIG.delayBetweenBatches}ms`);

    // 检查依赖
    try {
        require('jsdom');
        require('playwright');
    } catch (error) {
        console.error('缺少依赖包，请运行: npm install jsdom playwright');
        process.exit(1);
    }

    // 创建主输出目录
    if (!fs.existsSync(CONFIG.outputDir)) {
        fs.mkdirSync(CONFIG.outputDir, { recursive: true });
    }

    // 启动浏览器
    console.log('\n启动浏览器...');
    const browser = await chromium.launch({
        headless: true
    });

    let totalSuccess = 0;
    let totalFailed = 0;
    const allResults = [];

    try {
        // 处理每个主文档
        for (const mainFile of CONFIG.mainFiles) {
            const mainFilePath = path.join(CONFIG.baseDir, mainFile);

            if (!fs.existsSync(mainFilePath)) {
                console.error(`主文件不存在: ${mainFilePath}`);
                continue;
            }

            const result = await convertMainDocument(mainFile, browser);
            totalSuccess += result.success;
            totalFailed += result.failed;
            allResults.push({
                mainFile: mainFile,
                mainTitle: getMainFileTitle(mainFile),
                ...result
            });
        }

    } finally {
        await browser.close();
    }

    // 生成总体报告
    const reportFile = path.join(CONFIG.outputDir, 'batch_conversion_report.json');
    fs.writeFileSync(reportFile, JSON.stringify({
        totalFiles: totalSuccess + totalFailed,
        totalSuccess: totalSuccess,
        totalFailed: totalFailed,
        successRate: totalSuccess > 0 ? ((totalSuccess / (totalSuccess + totalFailed)) * 100).toFixed(2) + '%' : '0%',
        results: allResults,
        config: CONFIG,
        timestamp: new Date().toISOString()
    }, null, 2));

    console.log('\n==========================================');
    console.log('批量转换完成统计:');
    console.log(`总文件数: ${totalSuccess + totalFailed}`);
    console.log(`总成功: ${totalSuccess}`);
    console.log(`总失败: ${totalFailed}`);
    console.log(`成功率: ${totalSuccess > 0 ? ((totalSuccess / (totalSuccess + totalFailed)) * 100).toFixed(2) + '%' : '0%'}`);
    console.log(`输出目录: ${CONFIG.outputDir}`);
    console.log(`总体报告: ${reportFile}`);
}

// 执行主函数
if (require.main === module) {
    main().catch(error => {
        console.error('程序执行失败:', error);
        process.exit(1);
    });
}
