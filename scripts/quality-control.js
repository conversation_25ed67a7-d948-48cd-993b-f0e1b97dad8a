const fs = require('fs');
const path = require('path');
const { chromium } = require('playwright');

// 配置常量
const CONFIG = {
    outputDir: '/Users/<USER>/Desktop/Trea ai /data/help/pdf',
    minFileSize: 10 * 1024,  // 最小文件大小 10KB
    maxFileSize: 50 * 1024 * 1024,  // 最大文件大小 50MB
    sampleSize: 10,  // 随机抽样验证的文件数量
    mainDirs: ['网站后台', '功能详解', '二次开发']
};

/**
 * 检查PDF文件基本属性
 */
function checkPdfFile(filePath) {
    const stats = fs.statSync(filePath);
    const fileSize = stats.size;
    const fileName = path.basename(filePath);
    
    const issues = [];
    
    // 检查文件大小
    if (fileSize < CONFIG.minFileSize) {
        issues.push(`文件过小 (${fileSize} bytes)`);
    }
    if (fileSize > CONFIG.maxFileSize) {
        issues.push(`文件过大 (${(fileSize / 1024 / 1024).toFixed(2)} MB)`);
    }
    
    // 检查文件名格式
    if (!fileName.match(/^\d{3}_.*\.pdf$/)) {
        issues.push('文件名格式不正确');
    }
    
    // 检查文件是否可读
    try {
        fs.accessSync(filePath, fs.constants.R_OK);
    } catch (error) {
        issues.push('文件不可读');
    }
    
    return {
        fileName: fileName,
        fileSize: fileSize,
        fileSizeMB: (fileSize / 1024 / 1024).toFixed(2),
        issues: issues,
        isValid: issues.length === 0
    };
}

/**
 * 扫描目录中的所有PDF文件
 */
function scanPdfFiles(dirPath) {
    const files = [];
    
    if (!fs.existsSync(dirPath)) {
        return files;
    }
    
    const items = fs.readdirSync(dirPath);
    
    for (const item of items) {
        const itemPath = path.join(dirPath, item);
        const stats = fs.statSync(itemPath);
        
        if (stats.isFile() && item.endsWith('.pdf')) {
            const fileInfo = checkPdfFile(itemPath);
            files.push({
                path: itemPath,
                ...fileInfo
            });
        }
    }
    
    return files;
}

/**
 * 验证转换结果文件
 */
function validateConversionResults(dirPath) {
    const resultFile = path.join(dirPath, 'conversion_results.json');
    
    if (!fs.existsSync(resultFile)) {
        return {
            exists: false,
            error: '转换结果文件不存在'
        };
    }
    
    try {
        const data = JSON.parse(fs.readFileSync(resultFile, 'utf8'));
        
        return {
            exists: true,
            totalFiles: data.totalFiles || 0,
            successCount: data.successCount || 0,
            failCount: data.failCount || 0,
            successRate: data.successCount > 0 ? 
                ((data.successCount / data.totalFiles) * 100).toFixed(2) + '%' : '0%',
            timestamp: data.timestamp,
            isValid: true
        };
    } catch (error) {
        return {
            exists: true,
            error: `解析结果文件失败: ${error.message}`,
            isValid: false
        };
    }
}

/**
 * 随机抽样验证PDF内容
 */
async function sampleContentValidation(files, sampleSize) {
    if (files.length === 0) return [];
    
    // 随机选择文件
    const shuffled = [...files].sort(() => 0.5 - Math.random());
    const sample = shuffled.slice(0, Math.min(sampleSize, files.length));
    
    console.log(`\n开始内容抽样验证 (${sample.length} 个文件)...`);
    
    const browser = await chromium.launch({ headless: true });
    const results = [];
    
    try {
        for (let i = 0; i < sample.length; i++) {
            const file = sample[i];
            console.log(`[${i + 1}/${sample.length}] 验证: ${file.fileName}`);
            
            try {
                const page = await browser.newPage();
                
                // 尝试加载PDF文件
                await page.goto(`file://${file.path}`, { 
                    waitUntil: 'load',
                    timeout: 10000 
                });
                
                // 检查页面是否正常加载
                const title = await page.title();
                
                await page.close();
                
                results.push({
                    fileName: file.fileName,
                    status: 'success',
                    title: title,
                    message: '内容验证通过'
                });
                
            } catch (error) {
                results.push({
                    fileName: file.fileName,
                    status: 'failed',
                    error: error.message,
                    message: 'PDF内容验证失败'
                });
            }
        }
    } finally {
        await browser.close();
    }
    
    return results;
}

/**
 * 生成质量控制报告
 */
function generateQualityReport(allResults) {
    const report = {
        timestamp: new Date().toISOString(),
        summary: {
            totalDirectories: allResults.length,
            totalFiles: 0,
            validFiles: 0,
            invalidFiles: 0,
            totalSizeMB: 0,
            averageSizeMB: 0
        },
        directories: allResults,
        recommendations: []
    };
    
    // 计算统计信息
    allResults.forEach(dir => {
        if (dir.files) {
            report.summary.totalFiles += dir.files.length;
            dir.files.forEach(file => {
                if (file.isValid) {
                    report.summary.validFiles++;
                } else {
                    report.summary.invalidFiles++;
                }
                report.summary.totalSizeMB += parseFloat(file.fileSizeMB);
            });
        }
    });
    
    if (report.summary.totalFiles > 0) {
        report.summary.averageSizeMB = (report.summary.totalSizeMB / report.summary.totalFiles).toFixed(2);
    }
    
    // 生成建议
    if (report.summary.invalidFiles > 0) {
        report.recommendations.push(`发现 ${report.summary.invalidFiles} 个问题文件，建议重新转换`);
    }
    
    if (report.summary.totalSizeMB > 1000) {
        report.recommendations.push('总文件大小超过1GB，建议考虑压缩或分批处理');
    }
    
    if (report.summary.averageSizeMB > 10) {
        report.recommendations.push('平均文件大小较大，建议优化PDF生成参数');
    }
    
    return report;
}

/**
 * 主质量控制函数
 */
async function runQualityControl() {
    console.log('火鸟门户帮助文档 PDF质量控制工具');
    console.log('==================================');
    
    if (!fs.existsSync(CONFIG.outputDir)) {
        console.error(`输出目录不存在: ${CONFIG.outputDir}`);
        process.exit(1);
    }
    
    const allResults = [];
    
    // 检查每个主目录
    for (const mainDir of CONFIG.mainDirs) {
        const dirPath = path.join(CONFIG.outputDir, mainDir);
        console.log(`\n检查目录: ${mainDir}`);
        console.log('-'.repeat(30));
        
        if (!fs.existsSync(dirPath)) {
            console.log('  目录不存在');
            allResults.push({
                directory: mainDir,
                exists: false,
                error: '目录不存在'
            });
            continue;
        }
        
        // 扫描PDF文件
        const files = scanPdfFiles(dirPath);
        console.log(`  找到 ${files.length} 个PDF文件`);
        
        // 验证转换结果
        const conversionResults = validateConversionResults(dirPath);
        
        // 统计问题文件
        const validFiles = files.filter(f => f.isValid);
        const invalidFiles = files.filter(f => !f.isValid);
        
        console.log(`  有效文件: ${validFiles.length}`);
        console.log(`  问题文件: ${invalidFiles.length}`);
        
        if (invalidFiles.length > 0) {
            console.log('  问题详情:');
            invalidFiles.forEach(file => {
                console.log(`    ${file.fileName}: ${file.issues.join(', ')}`);
            });
        }
        
        // 内容抽样验证
        const sampleResults = await sampleContentValidation(validFiles, CONFIG.sampleSize);
        const sampleSuccess = sampleResults.filter(r => r.status === 'success').length;
        
        console.log(`  内容验证: ${sampleSuccess}/${sampleResults.length} 通过`);
        
        allResults.push({
            directory: mainDir,
            exists: true,
            files: files,
            validFiles: validFiles.length,
            invalidFiles: invalidFiles.length,
            conversionResults: conversionResults,
            sampleValidation: sampleResults,
            totalSizeMB: files.reduce((sum, f) => sum + parseFloat(f.fileSizeMB), 0).toFixed(2)
        });
    }
    
    // 生成质量报告
    const qualityReport = generateQualityReport(allResults);
    const reportFile = path.join(CONFIG.outputDir, 'quality_control_report.json');
    fs.writeFileSync(reportFile, JSON.stringify(qualityReport, null, 2));
    
    console.log('\n==================================');
    console.log('质量控制总结:');
    console.log(`总目录数: ${qualityReport.summary.totalDirectories}`);
    console.log(`总文件数: ${qualityReport.summary.totalFiles}`);
    console.log(`有效文件: ${qualityReport.summary.validFiles}`);
    console.log(`问题文件: ${qualityReport.summary.invalidFiles}`);
    console.log(`总大小: ${qualityReport.summary.totalSizeMB} MB`);
    console.log(`平均大小: ${qualityReport.summary.averageSizeMB} MB`);
    
    if (qualityReport.recommendations.length > 0) {
        console.log('\n建议:');
        qualityReport.recommendations.forEach(rec => {
            console.log(`  • ${rec}`);
        });
    }
    
    console.log(`\n详细报告: ${reportFile}`);
}

// 执行质量控制
if (require.main === module) {
    runQualityControl().catch(error => {
        console.error('质量控制失败:', error);
        process.exit(1);
    });
}
