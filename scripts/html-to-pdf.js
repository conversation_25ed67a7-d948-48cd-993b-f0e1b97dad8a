const puppeteer = require('puppeteer');
const fs = require('fs');
const path = require('path');

async function convertHtmlToPdf(htmlFilePath, outputPath) {
    const browser = await puppeteer.launch({
        headless: true,
        args: ['--no-sandbox', '--disable-setuid-sandbox']
    });
    
    const page = await browser.newPage();
    
    // 读取HTML文件内容
    const htmlContent = fs.readFileSync(htmlFilePath, 'utf8');
    
    // 设置页面内容
    await page.setContent(htmlContent, {
        waitUntil: 'networkidle0'
    });
    
    // 生成PDF
    await page.pdf({
        path: outputPath,
        format: 'A4',
        printBackground: true,
        margin: {
            top: '20px',
            right: '20px',
            bottom: '20px',
            left: '20px'
        }
    });
    
    await browser.close();
    console.log(`PDF已生成: ${outputPath}`);
}

// 批量转换函数
async function batchConvert() {
    const htmlFiles = [
        '/Users/<USER>/Desktop/Trea ai /data/help/help.kumanyun.com/help-240.html',
        '/Users/<USER>/Desktop/Trea ai /data/help/help.kumanyun.com/help-239.html',
        '/Users/<USER>/Desktop/Trea ai /data/help/help.kumanyun.com/help-278.html',
        '/Users/<USER>/Desktop/Trea ai /data/help/help.kumanyun.com/help-241.html'
    ];
    
    for (const htmlFile of htmlFiles) {
        if (fs.existsSync(htmlFile)) {
            const fileName = path.basename(htmlFile, '.html');
            const outputPath = `/Users/<USER>/Desktop/Trea ai /data/help/pdf/${fileName}.pdf`;
            
            // 确保输出目录存在
            const outputDir = path.dirname(outputPath);
            if (!fs.existsSync(outputDir)) {
                fs.mkdirSync(outputDir, { recursive: true });
            }
            
            await convertHtmlToPdf(htmlFile, outputPath);
        } else {
            console.log(`文件不存在: ${htmlFile}`);
        }
    }
}

// 执行批量转换
batchConvert().catch(console.error);