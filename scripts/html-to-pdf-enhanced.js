const { chromium } = require('playwright');
const fs = require('fs');
const path = require('path');
const { JSDOM } = require('jsdom');

// 配置常量
const CONFIG = {
    baseDir: '/Users/<USER>/Desktop/Trea ai /data/help/help.kumanyun.com',
    outputDir: '/Users/<USER>/Desktop/Trea ai /data/help/pdf-enhanced',
    testFiles: [
        'help-246-800.html'  // 专门测试有大量图片的页面
    ],
    pdfOptions: {
        format: 'A4',
        printBackground: true,
        margin: {
            top: '20px',
            right: '20px',
            bottom: '20px',
            left: '20px'
        }
    }
};

/**
 * 提取并修复HTML内容
 */
function extractAndFixContent(htmlFilePath) {
    try {
        const htmlContent = fs.readFileSync(htmlFilePath, 'utf8');
        const dom = new JSDOM(htmlContent);
        const document = dom.window.document;
        
        // 提取页面标题
        const titleElement = document.querySelector('.content h3.title');
        const pageTitle = titleElement ? titleElement.textContent.trim() : 
                         path.basename(htmlFilePath, '.html');
        
        // 提取面包屑导航
        const breadElement = document.querySelector('.bread');
        const breadcrumb = breadElement ? breadElement.textContent.trim() : '';
        
        // 提取主要内容区域
        const contentElement = document.querySelector('.content');
        if (!contentElement) {
            throw new Error('未找到内容区域');
        }
        
        // 统计和修复图片
        const images = contentElement.querySelectorAll('img');
        let validImageCount = 0;
        
        images.forEach((img, index) => {
            const src = img.getAttribute('src');
            if (src && src.startsWith('upload/')) {
                const imagePath = path.join(CONFIG.baseDir, src);
                
                // 检查图片文件是否存在
                if (fs.existsSync(imagePath)) {
                    // 转换为file:// URL，不进行URL编码
                    const fileUrl = `file://${imagePath.replace(/\\/g, '/')}`;
                    img.setAttribute('src', fileUrl);
                    
                    // 添加alt属性
                    if (!img.getAttribute('alt')) {
                        img.setAttribute('alt', `图片 ${index + 1}`);
                    }
                    
                    validImageCount++;
                    console.log(`    图片 ${index + 1}: ${src} -> 存在`);
                } else {
                    console.log(`    图片 ${index + 1}: ${src} -> 不存在`);
                    // 移除不存在的图片
                    img.remove();
                }
            }
        });
        
        // 创建完整的HTML文档
        const fullHtml = `
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>${pageTitle}</title>
    <style>
        body {
            font-family: "Microsoft YaHei", "PingFang SC", "Helvetica Neue", Arial, sans-serif;
            line-height: 1.6;
            color: #333;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #fff;
        }
        .breadcrumb {
            font-size: 14px;
            color: #666;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid #eee;
        }
        .title {
            font-size: 24px;
            font-weight: bold;
            color: #333;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #007bff;
        }
        .content {
            font-size: 15px;
            line-height: 1.8;
        }
        .content img {
            max-width: 100% !important;
            height: auto !important;
            display: block;
            margin: 15px auto;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            page-break-inside: avoid;
        }
        .content p {
            margin: 10px 0;
            page-break-inside: avoid;
        }
        .content strong {
            color: #007bff;
            font-weight: bold;
        }
        .content a {
            color: #007bff;
            text-decoration: none;
        }
        .lake-content {
            margin: 15px 0;
        }
        .ne-p {
            margin: 8px 0;
            line-height: 1.6;
        }
        @media print {
            body { margin: 0; }
            .content img { 
                max-width: 100% !important;
                page-break-inside: avoid;
            }
        }
    </style>
</head>
<body>
    ${breadcrumb ? `<div class="breadcrumb">${breadcrumb}</div>` : ''}
    <h1 class="title">${pageTitle}</h1>
    <div class="content">
        ${contentElement.innerHTML}
    </div>
</body>
</html>`;
        
        return {
            title: pageTitle,
            breadcrumb: breadcrumb,
            content: fullHtml,
            imageCount: validImageCount,
            hasImages: validImageCount > 0
        };
        
    } catch (error) {
        console.error(`提取内容失败: ${htmlFilePath}`, error);
        return null;
    }
}

/**
 * 转换HTML为PDF
 */
async function convertToPdf(htmlFilePath, outputPath, browser) {
    try {
        console.log(`\n开始转换: ${path.basename(htmlFilePath)}`);
        
        const page = await browser.newPage();
        
        // 设置视口
        await page.setViewportSize({ width: 1200, height: 800 });
        
        // 提取和修复内容
        const contentData = extractAndFixContent(htmlFilePath);
        if (!contentData) {
            throw new Error('无法提取页面内容');
        }
        
        console.log(`  标题: ${contentData.title}`);
        console.log(`  有效图片: ${contentData.imageCount} 张`);
        
        // 设置页面内容
        await page.setContent(contentData.content, {
            waitUntil: 'networkidle',
            timeout: 60000
        });
        
        // 如果有图片，等待加载
        if (contentData.hasImages) {
            console.log(`  等待图片加载...`);
            
            // 等待一段时间让图片开始加载
            await page.waitForTimeout(3000);
            
            // 检查图片加载状态
            const imageStatus = await page.evaluate(() => {
                const images = Array.from(document.images);
                const results = {
                    total: images.length,
                    loaded: 0,
                    failed: 0,
                    details: []
                };
                
                images.forEach((img, index) => {
                    const status = {
                        index: index + 1,
                        src: img.src,
                        complete: img.complete,
                        naturalWidth: img.naturalWidth,
                        naturalHeight: img.naturalHeight
                    };
                    
                    if (img.complete && img.naturalWidth > 0) {
                        results.loaded++;
                        status.status = 'loaded';
                    } else {
                        results.failed++;
                        status.status = 'failed';
                    }
                    
                    results.details.push(status);
                });
                
                return results;
            });
            
            console.log(`  图片状态: ${imageStatus.loaded}/${imageStatus.total} 加载成功`);
            
            // 显示失败的图片
            if (imageStatus.failed > 0) {
                console.log(`  失败的图片:`);
                imageStatus.details.filter(d => d.status === 'failed').forEach(d => {
                    console.log(`    图片 ${d.index}: ${d.src}`);
                });
            }
        }
        
        // 生成PDF
        console.log(`  生成PDF...`);
        await page.pdf({
            path: outputPath,
            ...CONFIG.pdfOptions
        });
        
        await page.close();
        
        // 检查文件大小
        const stats = fs.statSync(outputPath);
        const fileSizeMB = (stats.size / 1024 / 1024).toFixed(2);
        
        console.log(`  ✓ PDF生成成功 (${fileSizeMB} MB)`);
        
        return {
            success: true,
            fileSize: fileSizeMB,
            title: contentData.title,
            imageCount: contentData.imageCount
        };
        
    } catch (error) {
        console.error(`  ✗ 转换失败: ${error.message}`);
        throw error;
    }
}

/**
 * 主测试函数
 */
async function testEnhancedConversion() {
    console.log('火鸟门户帮助文档 增强版PDF转换测试');
    console.log('=====================================');
    
    // 创建输出目录
    if (!fs.existsSync(CONFIG.outputDir)) {
        fs.mkdirSync(CONFIG.outputDir, { recursive: true });
    }
    
    // 启动浏览器
    console.log('启动浏览器...');
    const browser = await chromium.launch({
        headless: true,
        args: ['--disable-web-security', '--allow-running-insecure-content']
    });
    
    try {
        for (const testFile of CONFIG.testFiles) {
            const inputPath = path.join(CONFIG.baseDir, testFile);
            const outputFileName = `enhanced_${testFile.replace('.html', '.pdf')}`;
            const outputPath = path.join(CONFIG.outputDir, outputFileName);
            
            if (!fs.existsSync(inputPath)) {
                console.log(`文件不存在: ${testFile}`);
                continue;
            }
            
            await convertToPdf(inputPath, outputPath, browser);
        }
        
    } finally {
        await browser.close();
    }
    
    console.log('\n=====================================');
    console.log('测试完成！');
    console.log(`输出目录: ${CONFIG.outputDir}`);
}

// 执行测试
if (require.main === module) {
    testEnhancedConversion().catch(error => {
        console.error('测试失败:', error);
        process.exit(1);
    });
}
