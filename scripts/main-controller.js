#!/usr/bin/env node

const fs = require('fs');
const path = require('path');
const { spawn } = require('child_process');

// 配置
const CONFIG = {
    baseDir: '/Users/<USER>/Desktop/Trea ai /data/help/help.kumanyun.com',
    outputDir: '/Users/<USER>/Desktop/Trea ai /data/help/pdf',
    scriptsDir: __dirname
};

// 可用的脚本
const SCRIPTS = {
    'analyze': {
        file: 'extract-links.js',
        description: '分析HTML文档结构和链接'
    },
    'test': {
        file: 'test-pdf-playwright.js',
        description: '测试PDF转换功能'
    },
    'convert': {
        file: 'html-to-pdf-batch.js',
        description: '批量转换HTML为PDF'
    },
    'quality': {
        file: 'quality-control.js',
        description: '质量控制和验证'
    }
};

/**
 * 显示帮助信息
 */
function showHelp() {
    console.log('火鸟门户帮助文档 HTML转PDF 主控制器');
    console.log('=====================================');
    console.log('');
    console.log('用法: node main-controller.js <命令> [选项]');
    console.log('');
    console.log('可用命令:');
    Object.entries(SCRIPTS).forEach(([cmd, info]) => {
        console.log(`  ${cmd.padEnd(10)} ${info.description}`);
    });
    console.log('');
    console.log('特殊命令:');
    console.log('  all        执行完整的转换流程 (analyze -> test -> convert -> quality)');
    console.log('  setup      检查和安装依赖');
    console.log('  status     显示当前状态和统计信息');
    console.log('  clean      清理输出目录');
    console.log('  help       显示此帮助信息');
    console.log('');
    console.log('示例:');
    console.log('  node main-controller.js analyze    # 分析文档结构');
    console.log('  node main-controller.js test       # 测试转换功能');
    console.log('  node main-controller.js convert    # 批量转换');
    console.log('  node main-controller.js all        # 完整流程');
}

/**
 * 执行脚本
 */
function runScript(scriptFile, args = []) {
    return new Promise((resolve, reject) => {
        const scriptPath = path.join(CONFIG.scriptsDir, scriptFile);
        
        if (!fs.existsSync(scriptPath)) {
            reject(new Error(`脚本文件不存在: ${scriptPath}`));
            return;
        }
        
        console.log(`执行: ${scriptFile}`);
        console.log('='.repeat(50));
        
        const child = spawn('node', [scriptPath, ...args], {
            stdio: 'inherit',
            cwd: CONFIG.scriptsDir
        });
        
        child.on('close', (code) => {
            if (code === 0) {
                console.log(`\n✓ ${scriptFile} 执行完成\n`);
                resolve();
            } else {
                reject(new Error(`${scriptFile} 执行失败，退出码: ${code}`));
            }
        });
        
        child.on('error', (error) => {
            reject(new Error(`启动 ${scriptFile} 失败: ${error.message}`));
        });
    });
}

/**
 * 检查依赖
 */
async function checkDependencies() {
    console.log('检查依赖...');
    
    const requiredPackages = ['jsdom', 'playwright'];
    const missingPackages = [];
    
    for (const pkg of requiredPackages) {
        try {
            require.resolve(pkg);
            console.log(`✓ ${pkg} 已安装`);
        } catch (error) {
            console.log(`✗ ${pkg} 未安装`);
            missingPackages.push(pkg);
        }
    }
    
    if (missingPackages.length > 0) {
        console.log(`\n缺少依赖包: ${missingPackages.join(', ')}`);
        console.log('请运行: npm install ' + missingPackages.join(' '));
        return false;
    }
    
    // 检查Playwright浏览器
    try {
        const { chromium } = require('playwright');
        await chromium.launch({ headless: true }).then(browser => browser.close());
        console.log('✓ Playwright浏览器可用');
    } catch (error) {
        console.log('✗ Playwright浏览器不可用');
        console.log('请运行: npx playwright install chromium');
        return false;
    }
    
    return true;
}

/**
 * 显示状态信息
 */
function showStatus() {
    console.log('当前状态信息');
    console.log('='.repeat(30));
    
    // 检查基础目录
    console.log(`基础目录: ${CONFIG.baseDir}`);
    console.log(`  存在: ${fs.existsSync(CONFIG.baseDir) ? '✓' : '✗'}`);
    
    // 检查输出目录
    console.log(`输出目录: ${CONFIG.outputDir}`);
    console.log(`  存在: ${fs.existsSync(CONFIG.outputDir) ? '✓' : '✗'}`);
    
    if (fs.existsSync(CONFIG.outputDir)) {
        const mainDirs = ['网站后台', '功能详解', '二次开发'];
        mainDirs.forEach(dir => {
            const dirPath = path.join(CONFIG.outputDir, dir);
            if (fs.existsSync(dirPath)) {
                const files = fs.readdirSync(dirPath).filter(f => f.endsWith('.pdf'));
                console.log(`  ${dir}: ${files.length} 个PDF文件`);
            } else {
                console.log(`  ${dir}: 目录不存在`);
            }
        });
    }
    
    // 检查主文件
    const mainFiles = ['help-4.html', 'help-5.html', 'help-7.html'];
    console.log('\n主文件状态:');
    mainFiles.forEach(file => {
        const filePath = path.join(CONFIG.baseDir, file);
        console.log(`  ${file}: ${fs.existsSync(filePath) ? '✓' : '✗'}`);
    });
}

/**
 * 清理输出目录
 */
function cleanOutput() {
    console.log('清理输出目录...');
    
    if (!fs.existsSync(CONFIG.outputDir)) {
        console.log('输出目录不存在，无需清理');
        return;
    }
    
    const items = fs.readdirSync(CONFIG.outputDir);
    let deletedCount = 0;
    
    items.forEach(item => {
        const itemPath = path.join(CONFIG.outputDir, item);
        const stats = fs.statSync(itemPath);
        
        if (stats.isDirectory()) {
            fs.rmSync(itemPath, { recursive: true, force: true });
            console.log(`删除目录: ${item}`);
            deletedCount++;
        } else if (item.endsWith('.json') || item.endsWith('.pdf')) {
            fs.unlinkSync(itemPath);
            console.log(`删除文件: ${item}`);
            deletedCount++;
        }
    });
    
    console.log(`清理完成，删除了 ${deletedCount} 个项目`);
}

/**
 * 执行完整流程
 */
async function runFullProcess() {
    console.log('开始执行完整转换流程...');
    console.log('='.repeat(50));
    
    try {
        // 1. 检查依赖
        console.log('步骤 1/5: 检查依赖');
        const depsOk = await checkDependencies();
        if (!depsOk) {
            throw new Error('依赖检查失败');
        }
        
        // 2. 分析文档结构
        console.log('\n步骤 2/5: 分析文档结构');
        await runScript('extract-links.js');
        
        // 3. 测试转换功能
        console.log('\n步骤 3/5: 测试转换功能');
        await runScript('test-pdf-playwright.js');
        
        // 4. 批量转换
        console.log('\n步骤 4/5: 批量转换');
        await runScript('html-to-pdf-batch.js');
        
        // 5. 质量控制
        console.log('\n步骤 5/5: 质量控制');
        await runScript('quality-control.js');
        
        console.log('='.repeat(50));
        console.log('✓ 完整转换流程执行成功！');
        console.log(`输出目录: ${CONFIG.outputDir}`);
        
    } catch (error) {
        console.error('='.repeat(50));
        console.error('✗ 流程执行失败:', error.message);
        process.exit(1);
    }
}

/**
 * 主函数
 */
async function main() {
    const args = process.argv.slice(2);
    const command = args[0];
    
    if (!command || command === 'help') {
        showHelp();
        return;
    }
    
    try {
        switch (command) {
            case 'setup':
                await checkDependencies();
                break;
                
            case 'status':
                showStatus();
                break;
                
            case 'clean':
                cleanOutput();
                break;
                
            case 'all':
                await runFullProcess();
                break;
                
            default:
                if (SCRIPTS[command]) {
                    await runScript(SCRIPTS[command].file, args.slice(1));
                } else {
                    console.error(`未知命令: ${command}`);
                    console.error('使用 "help" 查看可用命令');
                    process.exit(1);
                }
        }
    } catch (error) {
        console.error('执行失败:', error.message);
        process.exit(1);
    }
}

// 执行主函数
if (require.main === module) {
    main();
}
