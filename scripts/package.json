{"name": "html-to-pdf-converter", "version": "1.0.0", "description": "火鸟门户帮助文档HTML转PDF转换工具", "main": "html-to-pdf.js", "scripts": {"start": "node html-to-pdf.js", "install-deps": "npm install puppeteer jsdom", "test": "node -e \"console.log('依赖检查...'); require('puppeteer'); require('jsdom'); console.log('所有依赖已安装');\""}, "dependencies": {"jsdom": "^22.1.0", "playwright": "^1.54.1", "puppeteer": "^21.11.0"}, "keywords": ["html", "pdf", "converter", "puppeteer", "documentation"], "author": "Trea AI Assistant", "license": "MIT"}